//package com.siemens.spm.analysis.detectorreport.template;
//
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.google.gson.Gson;
//import com.siemens.spm.analysis.api.vo.DateRange;
//import com.siemens.spm.analysis.api.vo.detectorreport.DetectorScope;
//import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
//import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
//import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
//import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
//import com.siemens.spm.analysis.config.ReportTemplateConfig;
//import com.siemens.spm.analysis.detectorreport.util.DetectorTemplateBuilder;
//import com.siemens.spm.analysis.domain.DetectorMetric;
//import com.siemens.spm.analysis.domain.DetectorStatisticType;
//import com.siemens.spm.analysis.domain.DetectorTemplate;
//import com.siemens.spm.analysis.domain.TemplateSchedule;
//import com.siemens.spm.analysis.factory.TimezoneFactory;
//import com.siemens.spm.analysis.repository.DetectorIntersectionResultRepository;
//import com.siemens.spm.analysis.repository.DetectorResultRepository;
//import com.siemens.spm.analysis.repository.DetectorTemplateRepository;
//import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
//import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
//import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
//import com.siemens.spm.analysis.utils.BeanFinderMocker;
//import com.siemens.spm.common.util.BeanFinder;
//import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
//import com.siemens.spm.spmstudiosdk.exception.StudioException;
//import com.siemens.spm.spmstudiosdk.service.StudioUserService;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.time.LocalTime;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//
//import static com.siemens.spm.analysis.api.vo.DateRange.Unit.DAY;
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
///**
// * Unit tests for DetectorTemplateStrategyBean timezone handling functionality.
// *
// * This test class focuses on testing timezone resolution and validation in detector template creation,
// * similar to the performance metrics timezone tests. It uses JSON-based test data for realistic testing scenarios.
// *
// * Test scenarios covered:
// * - Valid timezone provided in request
// * - Null timezone (should resolve from agency)
// * - Empty timezone (should resolve from agency)
// * - TimezoneFactory failure scenarios
// * - Missing agency ID scenarios
// * - User not found scenarios
// *
// * @see PMTemplateAbnormalDataTest for similar performance metrics timezone testing approach
// */
//@ExtendWith(MockitoExtension.class)
//class DetectorTemplateTimezoneTest {
//
//    private final ObjectMapper objectMapper = new ObjectMapper();
//
//    @Mock
//    private StudioUserService studioUserService;
//
//    @Mock
//    private TemplateScheduleRepository templateScheduleRepository;
//
//    @Mock
//    private DetectorTemplateRepository detectorTemplateRepository;
//
//    @Mock
//    private TimezoneFactory timezoneFactory;
//
//    @InjectMocks
//    private DetectorTemplateStrategyBean bean;
//
//
//    ObjectMapper mockObjectMapper = Mockito.mock(ObjectMapper.class);
//
//    @BeforeEach
//    public void init(){
//        // JSON payload for detector template creation with valid timezone
//        MockedStatic<BeanFinder> beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
//        beanFinderMocked.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
//
//    }
//
//    @Test
//    void testCreateReportTemplate_withNonNullTimeZone_WillSuccess() throws StudioException, JsonProcessingException {
//        // --- General Data ---
//        var generalVO = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateGeneralVO();
//        generalVO.setName("DetectorRepport_DaoTQ1");
//        generalVO.setDescription("DetectorRepport_DaoTQ1");
//        generalVO.setWeekDays(Set.of(
//                java.time.DayOfWeek.MONDAY, java.time.DayOfWeek.TUESDAY, java.time.DayOfWeek.WEDNESDAY,
//                java.time.DayOfWeek.THURSDAY, java.time.DayOfWeek.FRIDAY, java.time.DayOfWeek.SATURDAY, java.time.DayOfWeek.SUNDAY
//        ));
//        generalVO.setAgencyId(1738);
//        generalVO.setDetectorScope(com.siemens.spm.analysis.api.vo.detectorreport.DetectorScope.ALL_DETECTORS);
//        generalVO.setDetectors(new java.util.ArrayList<>());
//        generalVO.setTimeZone("America/New_York");
//        generalVO.setStartTime(java.time.LocalTime.of(0, 0));
//        generalVO.setEndTime(java.time.LocalTime.of(23, 0));
//        generalVO.setAggregation(com.siemens.spm.analysis.api.vo.enums.TemplateAggregation.HOURLY);
//
//        var dateRange = new com.siemens.spm.analysis.api.vo.DateRange();
//        dateRange.setScope(com.siemens.spm.analysis.api.vo.DateRange.Scope.RELATIVE);
//        dateRange.setStartDate(null);
//        dateRange.setEndDate(null);
//        dateRange.setUnit(DAY);
//        dateRange.setOffset(1);
//        generalVO.setDateRange(dateRange);
//
//        var metric1 = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorMetricVO();
//        metric1.setKeyName("Occupancy");
//        metric1.setUnit("PERCENTAGE");
//        metric1.setStatistic(com.siemens.spm.analysis.domain.DetectorStatisticType.AVERAGE);
//        metric1.setCondition(null);
//        metric1.setEnabled(true);
//
//        var metric2 = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorMetricVO();
//        metric2.setKeyName("Activation");
//        metric2.setUnit("RAW COUNT");
//        metric2.setStatistic(com.siemens.spm.analysis.domain.DetectorStatisticType.TOTAL);
//        metric2.setCondition(null);
//        metric2.setEnabled(true);
//
//        var metric3 = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorMetricVO();
//        metric3.setKeyName("Volume");
//        metric3.setUnit("VPH");
//        metric3.setStatistic(com.siemens.spm.analysis.domain.DetectorStatisticType.AVERAGE);
//        metric3.setCondition(null);
//        metric3.setEnabled(true);
//
//        generalVO.setMetrics(java.util.List.of(metric1, metric2, metric3));
//        generalVO.setPhases(java.util.List.of(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16));
//
//        // --- Intersection ---
//        var intersectionIdsVO = new com.siemens.spm.common.shared.vo.IntersectionIdsVO();
//        intersectionIdsVO.setScope(com.siemens.spm.common.shared.domaintype.IntersectionScope.ALL_INTERSECTIONS);
//        intersectionIdsVO.setUuids(java.util.List.of("*"));
//
//        // --- Schedule ---
//        var scheduleVO = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO();
//        scheduleVO.setScope(com.siemens.spm.analysis.api.vo.enums.ScheduleScope.EVERY_DAY);
//        scheduleVO.setTime(java.time.LocalTime.parse("22:15"));
//        scheduleVO.setMailReceive(true);
//
//        var scheduleValue = new com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO.Value();
//        scheduleValue.setDate(java.time.LocalDate.of(2025, 6, 29));
//        scheduleValue.setOffset(1);
//        scheduleValue.setDayOfWeeks(Set.of(
//                java.time.DayOfWeek.MONDAY, java.time.DayOfWeek.TUESDAY, java.time.DayOfWeek.WEDNESDAY,
//                java.time.DayOfWeek.THURSDAY, java.time.DayOfWeek.FRIDAY, java.time.DayOfWeek.SATURDAY, java.time.DayOfWeek.SUNDAY
//        ));
//        scheduleValue.setDayOfMonths(Set.of(1));
//        scheduleVO.setValue(scheduleValue);
//
//        // --- Request VO ---
//        var requestVO = new com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO();
//        requestVO.setGeneralVO(generalVO);
//        requestVO.setIntersectionIdsVO(intersectionIdsVO);
//        requestVO.setScheduleVO(scheduleVO);
//
//        // --- Mocks and test execution ---
//        try (MockedStatic<com.siemens.spm.common.agency.utils.AgencyUtils> agencyUtilsMockedStatic = Mockito.mockStatic(com.siemens.spm.common.agency.utils.AgencyUtils.class);
//             MockedStatic<com.siemens.spm.common.security.SecurityUtils> securityUtilsMockedStatic = Mockito.mockStatic(com.siemens.spm.common.security.SecurityUtils.class);
//             MockedStatic<com.siemens.spm.analysis.detectorreport.util.DetectorTemplateBuilder> detectorTemplateBuilderStatic = Mockito.mockStatic(com.siemens.spm.analysis.detectorreport.util.DetectorTemplateBuilder.class);
//        ) {
//            agencyUtilsMockedStatic.when(com.siemens.spm.common.agency.utils.AgencyUtils::getAgencyId).thenReturn("1738");
//            securityUtilsMockedStatic.when(com.siemens.spm.common.security.SecurityUtils::getCurrentUserEmail).thenReturn("<EMAIL>");
//
//            com.siemens.spm.spmstudiosdk.dto.StudioUserDto mockUser = new com.siemens.spm.spmstudiosdk.dto.StudioUserDto();
//            mockUser.setId(21);
//            mockUser.setEmail("<EMAIL>");
//            mockUser.setFirstName("Test");
//            mockUser.setLastName("User");
//            Mockito.when(studioUserService.findByEmail(any())).thenReturn(java.util.Optional.of(mockUser));
//
//            Mockito.when(templateScheduleRepository.saveAndFlush(any())).thenReturn(getDefaultTemplateSchedule());
//            Mockito.when(detectorTemplateRepository.save(any())).thenReturn(getDefaultDetectorTemplate());
//            detectorTemplateBuilderStatic.when(()->DetectorTemplateBuilder.buildEntityFromCreateRequestVO(any())).thenReturn(getDefaultDetectorTemplate());
//            DetectorTemplateDetailResultObject resultObject = bean.createReportTemplate(requestVO);
//
//            // Then: Should succeed and call timezoneFactory with correct value
//            assertNotNull(resultObject);
//            assertEquals(DetectorTemplateDetailResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());
//        }
//    }
//
//
//    // Helper to create a default TemplateSchedule matching the test JSON
//    public static TemplateSchedule getDefaultTemplateSchedule() {
//        TemplateSchedule schedule = new TemplateSchedule();
//
//        schedule.setId(null); // Default or generate as needed
//        schedule.setTemplate(null); // Will be set if needed later
//        schedule.setDetectorTemplate(null); // Link to DetectorTemplate if applicable
//        schedule.setScope(ScheduleScope.EVERY_DAY); // Default scope
//        schedule.setTime(LocalTime.of(22, 15)); // Default time 22:15
//
//        // Default value as JSON string
//        String valueJson = "{\"date\":\"2025-06-29\",\"offset\":1}";
//        schedule.setValue(valueJson);
//
//        // These can remain null or be populated later
//        schedule.setMetricJson(null);
//        schedule.setDisplayJson(null);
//
//        return schedule;
//    }
//
//    public static DetectorTemplate getDefaultDetectorTemplate() throws JsonProcessingException {
//        DetectorTemplate template = new DetectorTemplate();
//
//        template.setId(1L);
//        template.setName("DetectorRepport_DaoTQ1");
//        template.setDescription("DetectorRepport_DaoTQ1");
//        template.setAgencyId(1738);
//        template.setOwnerId(21L);
//        template.setTimezoneId("America/New_York");
//        template.setStartTime(LocalTime.of(0, 0));
//        template.setEndTime(LocalTime.of(23, 0));
//        template.setAggregation(TemplateAggregation.HOURLY);
//
//        // Week days
//        List<String> weekDays = Arrays.asList(
//                 "THURSDAY", "TUESDAY", "WEDNESDAY", "SUNDAY", "MONDAY", "SATURDAY", "FRIDAY"
//        );
//        template.setWeekDaysJson("\"week_days\": [\n" +
//                "            \"MONDAY\",\n" +
//                "            \"TUESDAY\",\n" +
//                "            \"WEDNESDAY\",\n" +
//                "            \"THURSDAY\",\n" +
//                "            \"FRIDAY\",\n" +
//                "            \"SATURDAY\",\n" +
//                "            \"SUNDAY\"\n" +
//                "        ]");
//
//        // DateRangeJson
//        Map<String, Object> dateRangeJson = new HashMap<>();
//        dateRangeJson.put("scope", "RELATIVE");
//        dateRangeJson.put("unit", "DAY");
//        dateRangeJson.put("offset", 1);
//        template.setDateRangeJson(new Gson().toJson(dateRangeJson));
//
//        // DateRange object
//        DateRange dateRange = new DateRange();
//        dateRange.setScope(DateRange.Scope.RELATIVE);
//        dateRange.setUnit(DAY);
//        dateRange.setOffset(1);
//        template.setDateRange(dateRange);
//
//        // Metrics JSON
//        List<Map<String, Object>> metricsJson = new ArrayList<>();
//        metricsJson.add(createMetricJson("Occupancy", "PERCENTAGE", true, "AVERAGE", null));
//        metricsJson.add(createMetricJson("Activation", "RAW COUNT", true, "TOTAL", null));
//        metricsJson.add(createMetricJson("Volume", "VPH", true, "AVERAGE", null));
//        template.setMetricsJson(new ObjectMapper().writeValueAsString(metricsJson));
//
//        // Metric objects
//        List<DetectorMetric> metricList = new ArrayList<>();
//        metricList.add(new DetectorMetric("Occupancy", "PERCENTAGE", true, DetectorStatisticType.AVERAGE, null));
//        metricList.add(new DetectorMetric("Activation", "RAW COUNT", true, DetectorStatisticType.TOTAL, null));
//        metricList.add(new DetectorMetric("Volume", "VPH", true, DetectorStatisticType.AVERAGE, null));
//        template.setMetric(metricList);
//
//        template.setIntersectionList("*");
//        template.setDetectorScope(DetectorScope.ALL_DETECTORS);
//        template.setStatus(TemplateStatus.ACTIVE);
//        template.setDeleted(false);
//        template.setMailReceived(false);
//
//        // Phases
//        List<Integer> phases = new ArrayList<>();
//        for (int i = 1; i <= 16; i++) {
//            phases.add(i);
//        }
//        template.setPhases(phases);
//        template.setPhasesJson(new Gson().toJson(phases));
//
//        template.setDetectors(new ArrayList<>());
//        template.setDetectorsJson("");
//
//        template.setResults(null);
//        template.setSchedule(null);
//
//        return template;
//    }
//
//    private static Map<String, Object> createMetricJson(String key, String unit, boolean enabled, String stat, Object condition) {
//        Map<String, Object> metric = new HashMap<>();
//        metric.put("keyName", key);
//        metric.put("unit", unit);
//        metric.put("enabled", enabled);
//        metric.put("statistic", stat);
//        metric.put("condition", condition);
//        return metric;
//    }
//}