package com.siemens.spm.analysis.detectorreport.template.aggregation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

import com.siemens.spm.analysis.strategy.DetectorReportStrategy;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for WeeklyAggregationStrategy.
 * Tests the groupDayPeriodsIntoWeeklyPeriods method functionality.
 */
@ExtendWith(MockitoExtension.class)
class WeeklyAggregationStrategyTest {

    @Mock
    private DetectorReportStrategy detectorReportStrategy;

    private WeeklyAggregationStrategy weeklyAggregationStrategy;

    @BeforeEach
    void setUp() {
        weeklyAggregationStrategy = new WeeklyAggregationStrategy(detectorReportStrategy);
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_EmptyList() {
        // Given
        List<Pair<LocalDateTime, LocalDateTime>> dayPeriods = new ArrayList<>();

        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result = 
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(dayPeriods);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_NullInput() {
        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result = 
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_SingleWeek() {
        // Given - periods within the same week (Monday to Sunday)
        List<Pair<LocalDateTime, LocalDateTime>> dayPeriods = new ArrayList<>();
        
        // Monday 2025-01-06
        LocalDateTime mondayStart = LocalDateTime.of(2025, 1, 6, 9, 0);
        LocalDateTime mondayEnd = LocalDateTime.of(2025, 1, 6, 17, 0);
        dayPeriods.add(Pair.of(mondayStart, mondayEnd));
        
        // Wednesday 2025-01-08
        LocalDateTime wednesdayStart = LocalDateTime.of(2025, 1, 8, 10, 0);
        LocalDateTime wednesdayEnd = LocalDateTime.of(2025, 1, 8, 16, 0);
        dayPeriods.add(Pair.of(wednesdayStart, wednesdayEnd));
        
        // Friday 2025-01-10
        LocalDateTime fridayStart = LocalDateTime.of(2025, 1, 10, 8, 0);
        LocalDateTime fridayEnd = LocalDateTime.of(2025, 1, 10, 18, 0);
        dayPeriods.add(Pair.of(fridayStart, fridayEnd));

        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result = 
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(dayPeriods);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // One week
        assertEquals(3, result.get(0).size()); // Three periods in that week
        
        // Verify periods are sorted by start time (chronologically)
        List<Pair<LocalDateTime, LocalDateTime>> weekPeriods = result.get(0);
        assertEquals(mondayStart, weekPeriods.get(0).getFirst()); // Monday 9:00 comes first chronologically
        assertEquals(wednesdayStart, weekPeriods.get(1).getFirst()); // Wednesday 10:00 comes second
        assertEquals(fridayStart, weekPeriods.get(2).getFirst()); // Friday 8:00 comes last chronologically
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_MultipleWeeks() {
        // Given - periods spanning multiple weeks
        List<Pair<LocalDateTime, LocalDateTime>> dayPeriods = new ArrayList<>();
        
        // Week 1: Monday 2025-01-06
        LocalDateTime week1Start = LocalDateTime.of(2025, 1, 6, 9, 0);
        LocalDateTime week1End = LocalDateTime.of(2025, 1, 6, 17, 0);
        dayPeriods.add(Pair.of(week1Start, week1End));
        
        // Week 2: Tuesday 2025-01-14
        LocalDateTime week2Start = LocalDateTime.of(2025, 1, 14, 10, 0);
        LocalDateTime week2End = LocalDateTime.of(2025, 1, 14, 16, 0);
        dayPeriods.add(Pair.of(week2Start, week2End));
        
        // Week 2: Thursday 2025-01-16
        LocalDateTime week2ThursdayStart = LocalDateTime.of(2025, 1, 16, 8, 0);
        LocalDateTime week2ThursdayEnd = LocalDateTime.of(2025, 1, 16, 18, 0);
        dayPeriods.add(Pair.of(week2ThursdayStart, week2ThursdayEnd));

        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result = 
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(dayPeriods);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Two weeks
        assertEquals(1, result.get(0).size()); // One period in first week
        assertEquals(2, result.get(1).size()); // Two periods in second week
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_SpanningMultipleDays() {
        // Given - a period that spans multiple days
        List<Pair<LocalDateTime, LocalDateTime>> dayPeriods = new ArrayList<>();
        
        // Period spanning from Friday to Monday
        LocalDateTime start = LocalDateTime.of(2025, 1, 10, 22, 0); // Friday 10 PM
        LocalDateTime end = LocalDateTime.of(2025, 1, 13, 6, 0);    // Monday 6 AM
        dayPeriods.add(Pair.of(start, end));

        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result = 
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(dayPeriods);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // Should span two weeks
        
        // First week should have periods for Friday, Saturday, Sunday
        assertTrue(result.get(0).size() >= 3);
        
        // Second week should have period for Monday
        assertTrue(result.get(1).size() >= 1);
    }

    @Test
    void testGroupDayPeriodsIntoWeeklyPeriods_InvalidPeriods() {
        // Given - periods with invalid data (simulating what would happen after null filtering)
        List<Pair<LocalDateTime, LocalDateTime>> dayPeriods = new ArrayList<>();

        // Valid period
        LocalDateTime validStart = LocalDateTime.of(2025, 1, 6, 9, 0);
        LocalDateTime validEnd = LocalDateTime.of(2025, 1, 6, 17, 0);
        dayPeriods.add(Pair.of(validStart, validEnd));

        // Period with start after end (this should be filtered out by our implementation)
        LocalDateTime laterStart = LocalDateTime.of(2025, 1, 6, 18, 0);
        LocalDateTime earlierEnd = LocalDateTime.of(2025, 1, 6, 8, 0);
        dayPeriods.add(Pair.of(laterStart, earlierEnd));

        // When
        List<List<Pair<LocalDateTime, LocalDateTime>>> result =
            weeklyAggregationStrategy.groupDayPeriodsIntoWeeklyPeriods(dayPeriods);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Only one valid period should remain
        assertEquals(1, result.get(0).size()); // Only the valid period
    }
}
