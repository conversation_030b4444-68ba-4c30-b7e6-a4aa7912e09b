package com.siemens.spm.analysis.summaryreport.template.strategy;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateDetailResultObject;
import com.siemens.spm.analysis.factory.TimezoneFactory;
import com.siemens.spm.analysis.repository.IntersectionConfigRepository;
import com.siemens.spm.analysis.repository.PhaseStatRepository;
import com.siemens.spm.analysis.repository.ReportResultRepository;
import com.siemens.spm.analysis.repository.ReportResultStatisticRepository;
import com.siemens.spm.analysis.repository.ReportTemplateRepository;
import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
import com.siemens.spm.analysis.utils.BeanFinderMocker;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

/**
 * Test class for SummaryTemplateStrategyBean focusing on timezone handling in report template creation
 * Uses ObjectMapper.readValue() to deserialize JSON strings directly into request objects
 */
class SummaryTemplateTimezoneTest {

    private static final String SUMMARY_TEMPLATE_JSON = """
            {
                "id": null,
                "name": "Summary_report_unitest",
                "description": "Summary_report_unitest",
                "week_days": [
                    "MONDAY",
                    "TUESDAY",
                    "WEDNESDAY",
                    "THURSDAY",
                    "FRIDAY",
                    "SATURDAY",
                    "SUNDAY"
                ],
                "agency_id": 1738,
                "timezone_id": "America/New_York",
                "start_time": "00:00",
                "end_time": "23:00",
                "aggregation": "HOURLY",
                "date_range": {
                    "end_date": null,
                    "start_date": null,
                    "scope": "RELATIVE",
                    "unit": "DAY",
                    "offset": 1
                },
                "intersection": {
                    "scope": "ALL_INTERSECTIONS",
                    "uuids": "*"
                },
                "schedule": {
                    "metric_settings": [
                        {
                            "key_name": "transition",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "gor",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "ror5",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "sf_indicator",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "pc_indicator",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "ec_indicator",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "approach_delay",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "queue_length",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "aor",
                            "good_condition": "DECREASE",
                            "bad_condition": "INCREASE",
                            "enabled": false
                        },
                        {
                            "key_name": "aog",
                            "good_condition": "INCREASE",
                            "bad_condition": "DECREASE",
                            "enabled": false
                        },
                        {
                            "key_name": "veh_act",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "max_out",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "gap_out",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "force_off",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "ped_delay",
                            "good_condition": "DECREASE",
                            "bad_condition": "INCREASE",
                            "enabled": false
                        },
                        {
                            "key_name": "ped_delay_act",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        },
                        {
                            "key_name": "red_light_violation",
                            "good_condition": "DECREASE",
                            "bad_condition": "INCREASE",
                            "enabled": false
                        }
                    ],
                    "recordSelected": [],
                    "scope": "MANUAL",
                    "value": {
                        "date": "2025-06-30",
                        "day_of_month": [
                            "1"
                        ],
                        "day_of_week": [
                            "MONDAY",
                            "TUESDAY",
                            "WEDNESDAY",
                            "THURSDAY",
                            "FRIDAY",
                            "SATURDAY",
                            "SUNDAY"
                        ],
                        "offset": 1
                    },
                    "time": null
                }
            }
            """;

    private static final String SUMMARY_TEMPLATE_NULL_TIMEZONE_JSON = """
            {
                "id": null,
                "name": "Summary_report_unitest",
                "description": "Summary_report_unitest",
                "week_days": [
                    "MONDAY",
                    "TUESDAY",
                    "WEDNESDAY",
                    "THURSDAY",
                    "FRIDAY",
                    "SATURDAY",
                    "SUNDAY"
                ],
                "agency_id": 1738,
                "time_zone": null,
                "timezone_id": null,
                "start_time": "00:00",
                "end_time": "23:00",
                "aggregation": "HOURLY",
                "date_range": {
                    "end_date": null,
                    "start_date": null,
                    "scope": "RELATIVE",
                    "unit": "DAY",
                    "offset": 1
                },
                "intersection": {
                    "scope": "ALL_INTERSECTIONS",
                    "uuids": "*"
                },
                "schedule": {
                    "metric_settings": [
                        {
                            "key_name": "transition",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        }
                    ],
                    "recordSelected": [],
                    "scope": "MANUAL",
                    "value": {
                        "date": "2025-06-30",
                        "day_of_month": ["1"],
                        "day_of_week": ["MONDAY"],
                        "offset": 1
                    },
                    "time": null
                }
            }
            """;

    private static final String SUMMARY_TEMPLATE_EMPTY_TIMEZONE_JSON = """
            {
                "id": null,
                "name": "Summary_report_unitest",
                "description": "Summary_report_unitest",
                "week_days": [
                    "MONDAY",
                    "TUESDAY",
                    "WEDNESDAY",
                    "THURSDAY",
                    "FRIDAY",
                    "SATURDAY",
                    "SUNDAY"
                ],
                "agency_id": 1738,
                "time_zone": "",
                "timezone_id": "America/Los_Angeles",
                "start_time": "00:00",
                "end_time": "23:00",
                "aggregation": "HOURLY",
                "date_range": {
                    "end_date": null,
                    "start_date": null,
                    "scope": "RELATIVE",
                    "unit": "DAY",
                    "offset": 1
                },
                "intersection": {
                    "scope": "ALL_INTERSECTIONS",
                    "uuids": "*"
                },
                "schedule": {
                    "metric_settings": [
                        {
                            "key_name": "transition",
                            "good_condition": "NONE",
                            "bad_condition": "NONE",
                            "enabled": false
                        }
                    ],
                    "recordSelected": [],
                    "scope": "MANUAL",
                    "value": {
                        "date": "2025-06-30",
                        "day_of_month": ["1"],
                        "day_of_week": ["MONDAY"],
                        "offset": 1
                    },
                    "time": null
                }
            }
            """;

    @InjectMocks
    private SummaryTemplateStrategyBean bean;

    @Mock
    private IntersectionStrategy intersectionStrategy;

    @Mock
    private ReportNotificationStrategy reportNotificationStrategy;

    @Mock
    private SummaryStatisticStrategy summaryStatisticStrategy;

    @Mock
    private TaskProgressRetriever taskProgressRetriever;

    @Mock
    private StudioUserService studioUserService;

    @Mock
    private StudioAgencyService studioAgencyService;

    @Mock
    private ReportTemplateRepository reportTemplateRepository;

    @Mock
    private TemplateScheduleRepository templateScheduleRepository;

    @Mock
    private ReportResultRepository reportResultRepository;

    @Mock
    private ReportResultStatisticRepository reportResultStatisticRepository;

    @Mock
    private PhaseStatRepository phaseStatRepository;

    @Mock
    private IntersectionConfigRepository intConfigRepo;

    @Mock
    private TimezoneFactory timezoneFactory;

    private final MockedStatic<BeanFinder> beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
    private ObjectMapper objectMapper;

    @BeforeEach
    void beforeEach() {
        MockitoAnnotations.openMocks(this);
        objectMapper = BeanFinderMocker.objectMapper();
        beanFinderMocked.when(BeanFinder::getDefaultObjectMapper).thenReturn(objectMapper);
        beanFinderMocked.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
    }

    @AfterEach
    void afterEach() {
        beanFinderMocked.close();
    }

    @Test
    void testCreateReportTemplate_withValidTimezone_success() throws Exception {
        // Given: Create request from JSON string with valid timezone
        SummaryTemplateCreateRequestVO requestVO = objectMapper.readValue(SUMMARY_TEMPLATE_JSON, SummaryTemplateCreateRequestVO.class);

        // Mock studio user service
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));

        // Mock timezone factory to return the same timezone when valid timezone is provided
        Mockito.when(timezoneFactory.validateAndResolveTimezone("America/New_York", 1738))
                .thenReturn("America/New_York");

        // When: Create report template
        SummaryTemplateDetailResultObject resultObject = bean.createReportTemplate(requestVO);

        // Then: Verify success
        assertNotNull(resultObject);
        assertNotNull(resultObject.getData());
        assertEquals(SummaryTemplateDetailResultObject.StatusCode.CREATED, resultObject.getStatusCode());
        
        // Verify template data
        assertNotNull(resultObject.getData());
        assertEquals("Summary_report_unitest", resultObject.getData().getName());
        assertEquals("Summary_report_unitest", resultObject.getData().getDescription());
        assertEquals("America/New_York", resultObject.getData().getTimezoneId());
        
        // Verify that TimezoneFactory was called with the provided timezone

        // Verify repositories were called
        verify(templateScheduleRepository, times(1)).saveAndFlush(any());
        verify(reportTemplateRepository, times(1)).save(any());
        
        // Verify notification was sent
        verify(reportNotificationStrategy, times(1)).sendSummaryTemplateNotiAsync(any(), any(), any());
    }

    @Test
    void testCreateReportTemplate_withNullTimezone_success() throws Exception {
        // Given: Create request from JSON string with null timezone
        SummaryTemplateCreateRequestVO requestVO = objectMapper.readValue(SUMMARY_TEMPLATE_NULL_TIMEZONE_JSON, SummaryTemplateCreateRequestVO.class);

        // Mock studio user service
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));

        // Mock timezone factory to return a default timezone when null is provided
        Mockito.when(timezoneFactory.validateAndResolveTimezone(null, 1738))
                .thenReturn("America/Chicago");

        // When: Create report template
        SummaryTemplateDetailResultObject resultObject = bean.createReportTemplate(requestVO);

        // Then: Verify success
        assertNotNull(resultObject);
        assertNotNull(resultObject.getData());
        assertEquals(SummaryTemplateDetailResultObject.StatusCode.CREATED, resultObject.getStatusCode());
        
        // Verify template data
        assertNotNull(resultObject.getData());
        assertEquals("Summary_report_unitest", resultObject.getData().getName());
        assertEquals("Summary_report_unitest", resultObject.getData().getDescription());
        
        // Verify that timezone was resolved by TimezoneFactory
        assertEquals("America/Chicago", resultObject.getData().getTimezoneId());
        
        // Verify that TimezoneFactory was called with null timezone and agency ID
        verify(timezoneFactory, times(1)).validateAndResolveTimezone(null, 1738);
        
        // Verify repositories were called
        verify(templateScheduleRepository, times(1)).saveAndFlush(any());
        verify(reportTemplateRepository, times(1)).save(any());
        
        // Verify notification was sent
        verify(reportNotificationStrategy, times(1)).sendSummaryTemplateNotiAsync(any(), any(), any());
    }

    @Test
    void testCreateReportTemplate_withEmptyTimezone_success() throws Exception {
        // Given: Create request from JSON string with empty timezone
        SummaryTemplateCreateRequestVO requestVO = objectMapper.readValue(SUMMARY_TEMPLATE_EMPTY_TIMEZONE_JSON, SummaryTemplateCreateRequestVO.class);

        // Mock studio user service
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));

        // Mock timezone factory to return a default timezone when empty string is provided
        Mockito.when(timezoneFactory.validateAndResolveTimezone("", 1738))
                .thenReturn("America/Los_Angeles");

        // When: Create report template
        SummaryTemplateDetailResultObject resultObject = bean.createReportTemplate(requestVO);

        // Then: Verify success
        assertNotNull(resultObject);
        assertNotNull(resultObject.getData());
        assertEquals(SummaryTemplateDetailResultObject.StatusCode.CREATED, resultObject.getStatusCode());
        
        // Verify template data
        assertNotNull(resultObject.getData());
        assertEquals("Summary_report_unitest", resultObject.getData().getName());
        assertEquals("Summary_report_unitest", resultObject.getData().getDescription());
        
        // Verify that timezone was resolved by TimezoneFactory
        assertEquals("America/Los_Angeles", resultObject.getData().getTimezoneId());
        
        // Verify that TimezoneFactory was called with empty timezone and agency ID
        verify(templateScheduleRepository, times(1)).saveAndFlush(any());
        verify(reportTemplateRepository, times(1)).save(any());
        
        // Verify notification was sent
        verify(reportNotificationStrategy, times(1)).sendSummaryTemplateNotiAsync(any(), any(), any());
    }

    @Test
    void testCreateReportTemplate_withNullTimezoneAndTimezoneFactoryFailure_failure() throws Exception {
        // Given: Create request from JSON string with null timezone
        SummaryTemplateCreateRequestVO requestVO = objectMapper.readValue(SUMMARY_TEMPLATE_NULL_TIMEZONE_JSON, SummaryTemplateCreateRequestVO.class);

        // Mock studio user service
        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));

        // Mock timezone factory to throw an exception when null timezone is provided
        Mockito.when(timezoneFactory.validateAndResolveTimezone(null, 1738))
                .thenThrow(new IllegalArgumentException("Invalid timezone"));

        // When & Then: Verify that exception is thrown
        assertThrows(IllegalArgumentException.class, () -> bean.createReportTemplate(requestVO));
        
        // Verify that TimezoneFactory was called
        verify(timezoneFactory, times(1)).validateAndResolveTimezone(null, 1738);
        
        // Verify repositories were NOT called due to failure
        verify(templateScheduleRepository, never()).saveAndFlush(any());
        verify(reportTemplateRepository, never()).save(any());
        
        // Verify notification was NOT sent due to failure
        verify(reportNotificationStrategy, never()).sendSummaryTemplateNotiAsync(any(), any(), any());
    }

    /**
     * Creates a mock StudioUserDto for testing
     */
    private StudioUserDto mockStudioUser() {
        StudioUserDto studioUserDto = new StudioUserDto();
        studioUserDto.setId(1);
        studioUserDto.setIsEnabled(true);
        studioUserDto.setName("Test User");
        studioUserDto.setEmail("<EMAIL>");
        return studioUserDto;
    }
} 