package com.siemens.spm.analysis.summaryreport.result.util;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.DateRange.Scope;
import com.siemens.spm.analysis.api.vo.DateRange.Unit;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryResultUnitDataVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportDataVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportInfoVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.Trending;
import com.siemens.spm.analysis.domain.ReportMetric;
import com.siemens.spm.analysis.domain.ReportResult;
import com.siemens.spm.analysis.domain.ReportResultStatistic;
import com.siemens.spm.analysis.domain.ReportTemplate;
import com.siemens.spm.analysis.utils.BeanFinderMocker;
import com.siemens.spm.common.util.BeanFinder;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

class ReportResultBuilderTest {

    private static final String TIME_ZONE = "GMT+7";

    private static final String INT_UUID = "4515820a-429c-4705-9b7d-f8101ed48856";

    private static final int AGENCY_UUID = 1;

    private static MockedStatic<BeanFinder> beanFinderMock;

    @BeforeAll
    public static void init() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
    }

    @AfterAll
    public static void afterTestClass() {
        beanFinderMock.close();
    }

    @Test
    void testBuildFromReportTemplate_scopeSpecific() throws JsonProcessingException {
        DateRange dateRange = DateRange.builder()
                .scope(Scope.SPECIFIC)
                .startDate(LocalDate.of(2021, 2, 20))
                .endDate(LocalDate.of(2021, 2, 22))
                .build();

        LocalTime startTime = LocalTime.of(2, 0);
        LocalTime endTime = LocalTime.of(4, 0);
        ReportTemplate template = ReportTemplate.builder()
                .dateRange(BeanFinderMocker.objectMapper().writeValueAsString(dateRange))
                .timezone(TIME_ZONE)
                .startTime(startTime)
                .endTime(endTime)
                .build();

        ReportResult reportResult = ReportResultBuilder.buildFromReportTemplate(template, "America/Adak");

        Assertions.assertNotNull(reportResult);
        Assertions.assertEquals(LocalDateTime.of(2021, 2, 20, 2, 0), reportResult.getFromTime());
        Assertions.assertEquals(LocalDateTime.of(2021, 2, 22, 4, 0), reportResult.getToTime());
    }

    @Test
    void testBuildFromReportTemplate_relativeWeeks() throws JsonProcessingException {
        LocalDate nowDate = LocalDate.of(2025, 6, 25);

        DateRange dateRange = DateRange.builder()
                .scope(Scope.RELATIVE)
                .unit(Unit.WEEK)
                .offset(2)
                .build();

        LocalTime startTime = LocalTime.of(2, 0);
        LocalTime endTime = LocalTime.of(4, 0);
        ReportTemplate template = ReportTemplate.builder()
                .dateRange(BeanFinderMocker.objectMapper().writeValueAsString(dateRange))
                .timezone(TIME_ZONE)
                .startTime(startTime)
                .endTime(endTime)
                .build();

        ReportResult reportResult = ReportResultBuilder.buildFromReportTemplate(template, "America/Adak");

        Assertions.assertNotNull(reportResult);
    }

    @Test
    void testBuildFromReportTemplate_relativeMonths() throws JsonProcessingException {
        LocalDate nowDate = LocalDate.of(2025, 6, 25);

        DateRange dateRange = DateRange.builder()
                .scope(Scope.RELATIVE)
                .unit(Unit.MONTH)
                .offset(2)
                .build();

        LocalTime startTime = LocalTime.of(2, 0);
        LocalTime endTime = LocalTime.of(4, 0);
        ReportTemplate template = ReportTemplate.builder()
                .dateRange(BeanFinderMocker.objectMapper().writeValueAsString(dateRange))
                .timezone(TIME_ZONE)
                .startTime(startTime)
                .endTime(endTime)
                .build();

        ReportResult reportResult = ReportResultBuilder.buildFromReportTemplate(template, "America/Adak");

        LocalDate firstDayOfMonth = nowDate.withDayOfMonth(1);

        Assertions.assertNotNull(reportResult);
        Assertions.assertEquals(LocalDateTime.of(firstDayOfMonth.minusMonths(2), startTime),
                reportResult.getFromTime());
        Assertions.assertEquals(LocalDateTime.of(firstDayOfMonth.minusDays(1), endTime), reportResult.getToTime());
    }

    @Test
    void testBuildSummaryReportDataVO_fromCurAndPrev() {
        ReportResultStatistic curStatistic = ReportResultStatistic.builder()
                .intUUID(INT_UUID)
                .aor("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .aog("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .vehicleActivation("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .maxOut("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .gapOut("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .forceOff("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .avgPedDelay("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .pedDelayActivation("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .approachDelay("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .queueLength("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .ror5("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .gor("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .ecIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .sfIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .pcIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .transition("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .build();

        ReportResultStatistic prevStatistic = ReportResultStatistic.builder()
                .intUUID(INT_UUID)
                .aor("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .aog("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .vehicleActivation("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .maxOut("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .gapOut("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .forceOff("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .avgPedDelay("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .pedDelayActivation("{\"max\":0.0,\"avg\":0.0,\"min\":0.0}")
                .approachDelay("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .queueLength("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .ror5("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .gor("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .ecIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .sfIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .pcIndicator("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .transition("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .build();

        SummaryReportDataVO summaryReportDataVO = ReportResultBuilder
                .buildSummaryReportDataVO(curStatistic, prevStatistic, ReportMetric.defaultAllMetrics());

        Assertions.assertNotNull(summaryReportDataVO);

        // AOR
        SummaryResultUnitDataVO aorUnitDataVO = summaryReportDataVO.getAor();
        Assertions.assertNotNull(aorUnitDataVO);
        Assertions.assertEquals(4.0, aorUnitDataVO.getMax().getValue());
        Assertions.assertEquals(Trending.BAD, aorUnitDataVO.getMax().getTrending());
        Assertions.assertEquals(4.0, aorUnitDataVO.getMax().getFluctuation());

        // AOG
        SummaryResultUnitDataVO aogUnitDataVO = summaryReportDataVO.getAog();
        Assertions.assertNotNull(aogUnitDataVO);
        Assertions.assertEquals(4.0, aogUnitDataVO.getMax().getValue());
        Assertions.assertEquals(Trending.GOOD, aogUnitDataVO.getMax().getTrending());
        Assertions.assertEquals(4.0, aogUnitDataVO.getMax().getFluctuation());
    }

    @Test
    void testBuildSummaryReportDataVO_withTrending() {
        ReportResultStatistic statistic = ReportResultStatistic.builder()
                .intUUID(INT_UUID)
                .aor("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .aog("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .vehicleActivation("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .maxOut("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .gapOut("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .forceOff("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .avgPedDelay("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .pedDelayActivation("{\"max\":4.0,\"avg\":3.0,\"min\":2.0}")
                .build();

        SummaryReportDataVO summaryReportDataVO = ReportResultBuilder
                .buildSummaryReportDataVO(statistic, Trending.NONE);

        Assertions.assertNotNull(summaryReportDataVO);

        // AOR
        SummaryResultUnitDataVO aorUnitDataVO = summaryReportDataVO.getAor();
        Assertions.assertNotNull(aorUnitDataVO);
        Assertions.assertEquals(4.0, aorUnitDataVO.getMax().getValue());
        Assertions.assertEquals(Trending.NONE, aorUnitDataVO.getMax().getTrending());
        Assertions.assertNull(aorUnitDataVO.getMax().getFluctuation());

        // AOG
        SummaryResultUnitDataVO aogUnitDataVO = summaryReportDataVO.getAog();
        Assertions.assertNotNull(aogUnitDataVO);
        Assertions.assertEquals(4.0, aogUnitDataVO.getMax().getValue());
        Assertions.assertEquals(Trending.NONE, aogUnitDataVO.getMax().getTrending());
        Assertions.assertNull(aogUnitDataVO.getMax().getFluctuation());
    }

    @Test
    void testBuildInfoVOFromReportResult() {
        LocalDateTime fromTime = LocalDateTime.now();
        LocalDateTime toTime = fromTime.plusHours(1);

        ReportResult reportResult = ReportResult.builder()
                .id(0L)
                .name("Name")
                .description("Desc")
                .agencyId(AGENCY_UUID)
                .weekDays("[\"MONDAY\",\"FRIDAY\",\"SATURDAY\",\"TUESDAY\",\"SUNDAY\",\"WEDNESDAY\",\"THURSDAY\"]")
                .timezone("+07:00")
                .aggregation(TemplateAggregation.HOURLY)
                .createdAt(Timestamp.valueOf(fromTime))
                .fromTime(fromTime)
                .toTime(toTime)
                .ownerId(0L)
                .build();

        SummaryReportInfoVO summaryReportInfoVO = ReportResultBuilder.buildInfoVOFromReportResult(reportResult);

        Assertions.assertNotNull(summaryReportInfoVO);
        Assertions.assertEquals(0L, summaryReportInfoVO.getId());
        Assertions.assertEquals("Name", summaryReportInfoVO.getName());
        Assertions.assertEquals("Desc", summaryReportInfoVO.getDescription());
        Assertions.assertEquals(AGENCY_UUID, summaryReportInfoVO.getAgencyId());
        Assertions.assertEquals("+07:00", summaryReportInfoVO.getTimeZone());
        Assertions.assertEquals(fromTime.toLocalDate(), summaryReportInfoVO.getFromDate());
        Assertions.assertEquals(0L, summaryReportInfoVO.getOwnerId());

        Assertions.assertNotNull(summaryReportInfoVO.getWeekDays());
        Assertions.assertEquals(7, summaryReportInfoVO.getWeekDays().size());
    }

}
