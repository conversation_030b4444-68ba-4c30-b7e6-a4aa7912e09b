//package com.siemens.spm.analysis.performancemetric.template.strategy;
//
//import java.time.DayOfWeek;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.LocalTime;
//import java.time.ZoneOffset;
//import java.util.Set;
//
//import com.siemens.spm.analysis.api.vo.DateRange;
//import com.siemens.spm.analysis.api.vo.response.AnalysisResultObject;
//import com.siemens.spm.analysis.domain.PMMetadata;
//import com.siemens.spm.analysis.domain.PMResult;
//import com.siemens.spm.analysis.domain.PMTemplate;
//import com.siemens.spm.analysis.performancemetric.template.exception.PMTemplateProcessingException;
//import com.siemens.spm.analysis.performancemetric.template.strategy.PMCalculatorBean;
//import com.siemens.spm.analysis.strategy.AbnormalAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.AogAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.AorAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.AppDelayAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.CoordHealthAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.CoordinationAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.MOEAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.PedAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.PpAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.PtAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.QueueLengthAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.SplitFailureAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.SplitMonitorAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.TurningMovementAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.VolumeAnalysisStrategy;
//import com.siemens.spm.analysis.strategy.YellowTrapStrategy;
//import com.siemens.spm.analysis.utils.BeanFinderMocker;
//import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
//import com.siemens.spm.common.util.BeanFinder;
//import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.eq;
//
//class PMCalculatorBeanTest {
//
//    private static final int AGENCY_ID = 1;
//    private static final String INT_ID = "Bar";
//
//    @InjectMocks
//    private PMCalculatorBean pmCalculatorBean;
//
//    private final MockedStatic<BeanFinder> beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
//
//    @Mock
//    private AorAnalysisStrategy aorAnalysisStrategy;
//
//    @Mock
//    private AogAnalysisStrategy aogAnalysisStrategy;
//
//    @Mock
//    private PtAnalysisStrategy ptAnalysisStrategy;
//
//    @Mock
//    private CoordinationAnalysisStrategy coordAnalysisStrategy;
//
//    @Mock
//    private PedAnalysisStrategy pedAnalysisStrategy;
//
//    @Mock
//    private CoordHealthAnalysisStrategy coordHealthAnalysisStrategy;
//
//    @Mock
//    private SplitFailureAnalysisStrategy splitFailureAnalysisStrategy;
//
//    @Mock
//    private AppDelayAnalysisStrategy appDelayAnalysisStrategy;
//
//    @Mock
//    private QueueLengthAnalysisStrategy queueLengthAnalysisStrategy;
//
//    @Mock
//    private VolumeAnalysisStrategy volumeAnalysisStrategy;
//
//    @Mock
//    private YellowTrapStrategy yellowTrapStrategy;
//
//    @Mock
//    private AbnormalAnalysisStrategy abnormalAnalysisStrategy;
//
//    @Mock
//    private SplitMonitorAnalysisStrategy splitMonitorAnalysisStrategy;
//
//    @Mock
//    private TurningMovementAnalysisStrategy turningMovementAnalysisStrategy;
//
//    @Mock
//    private PpAnalysisStrategy ppAnalysisStrategy;
//
//    @Mock
//    private MOEAnalysisStrategy moeAnalysisStrategy;
//
//    @BeforeEach
//    void beforeEach() {
//        MockitoAnnotations.openMocks(this);
//        beanFinderMocked.when(BeanFinder::getDefaultObjectMapper).thenReturn(BeanFinderMocker.objectMapper());
//        beanFinderMocked.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
//    }
//
//    @AfterEach
//    void afterEach() {
//        beanFinderMocked.close();
//    }
//
//    @Test
//    void testProcessAorAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(aorAnalysisStrategy
//                        .getAorAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.ARRIVALS_ON_RED),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessAogAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(aogAnalysisStrategy
//                        .getAogAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.ARRIVALS_ON_GREEN),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessPtAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(ptAnalysisStrategy
//                        .getPtAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.PHASE_TERMINATION),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessCoordinationAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(coordAnalysisStrategy
//                        .getCoordinationAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.COORDINATION),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessPedAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(pedAnalysisStrategy
//                        .getPedestrianAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.PEDESTRIAN),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessCoordinationHealthAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(coordHealthAnalysisStrategy
//                        .getCoordinationHealthAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.COORDINATION_HEALTH),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessSplitFailureAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(splitFailureAnalysisStrategy
//                        .getSplitFailureAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.SPLIT_FAILURE),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessApproachDelayAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(appDelayAnalysisStrategy
//                        .getAppDelayAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.APPROACH_DELAY),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessQueueLengthAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(queueLengthAnalysisStrategy
//                        .getQueueLengthAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.QUEUE_LENGTH),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessVolumeAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(volumeAnalysisStrategy
//                        .getVolumeAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.VOLUME),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessSplitMonitorAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(splitMonitorAnalysisStrategy
//                        .getSplitMonitorAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.SPLIT_MONITOR),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessTurningMovementAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(turningMovementAnalysisStrategy
//                        .getTurningMovementAnalysis(eq(AGENCY_ID), eq(INT_ID),
//                                any(LocalDateTime.class), any(LocalDateTime.class), anyInt()))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.TURNING_MOVEMENT),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessPpAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(ppAnalysisStrategy
//                        .getPpAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.PREEMPTION_PRIORITY),
//                mockIntersectionInternalVO());
//
//        Assertions.assertNotNull(result);
//    }
//
//    @Test
//    void testProcessMOEAnalysis() throws PMTemplateProcessingException {
//        Mockito.when(moeAnalysisStrategy
//                        .getMOEAnalysis(eq(AGENCY_ID), eq(INT_ID), any(LocalDateTime.class), any(LocalDateTime.class)))
//                .thenReturn(new AnalysisResultObject<>(AnalysisResultObject.StatusCode.SUCCESS));
//        PMResult result = pmCalculatorBean.process(mockTemplate(AnalysisType.MOE_ANALYSIS),
//                mockIntersectionInternalVO());
//        Assertions.assertNotNull(result);
//    }
//
//    private PMTemplate mockTemplate(AnalysisType metricType) {
//        return PMTemplate.builder()
//                .agencyId(AGENCY_ID)
//                .timezone(ZoneOffset.UTC.getId())
//                .metricType(metricType)
//                .metadata(PMMetadata.builder()
//                        .fromTime(LocalTime.of(9, 0))
//                        .toTime(LocalTime.of(12, 0))
//                        .binSize(900)
//                        .build())
//                .dateRange(DateRange.builder()
//                        .scope(DateRange.Scope.SPECIFIC)
//                        .startDate(LocalDate.of(2022, 10, 6))
//                        .endDate(LocalDate.of(2022, 10, 8))
//                        .build())
//                .weekDays(Set.of(DayOfWeek.THURSDAY, DayOfWeek.FRIDAY, DayOfWeek.SATURDAY))
//                .build();
//    }
//
//    private IntersectionInternalVO mockIntersectionInternalVO() {
//        return IntersectionInternalVO.builder()
//                .id(INT_ID)
//                .name("Foo_Bar")
//                .build();
//    }
//
//}
