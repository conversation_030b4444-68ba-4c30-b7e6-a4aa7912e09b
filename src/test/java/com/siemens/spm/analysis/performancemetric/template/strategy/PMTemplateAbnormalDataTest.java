//package com.siemens.spm.analysis.performancemetric.template.strategy;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.siemens.spm.analysis.api.vo.DateRange;
//import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
//import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
//import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateDetailVO;
//import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateSimpleDataVO;
//import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateCreateRequestVO;
//import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateDetailResultObject;
//import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateManipulateResultObject;
//import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateManipulateResultObject.PMTemplateManipulateStatusCode;
//import com.siemens.spm.analysis.domain.PMMetadata;
//import com.siemens.spm.analysis.domain.PMTemplate;
//import com.siemens.spm.analysis.domain.TemplateSchedule;
//import com.siemens.spm.analysis.factory.TimezoneFactory;
//import com.siemens.spm.analysis.repository.PMResultRepository;
//import com.siemens.spm.analysis.repository.PMResultSharedDataRepository;
//import com.siemens.spm.analysis.repository.PMTemplateRepository;
//import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
//import com.siemens.spm.analysis.strategy.intercom.AgencyStrategy;
//import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
//import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
//import com.siemens.spm.analysis.utils.BeanFinderMocker;
//import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
//import com.siemens.spm.common.util.BeanFinder;
//import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
//import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
//import com.siemens.spm.spmstudiosdk.service.StudioUserService;
//import com.siemens.spm.usermanagementservice.api.vo.response.IntersectionsInAgencyVerifyResultObject;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//
//import java.time.LocalTime;
//import java.util.ArrayList;
//import java.util.Optional;
//
//import static org.junit.jupiter.api.Assertions.assertEquals;
//import static org.junit.jupiter.api.Assertions.assertNotNull;
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyCollection;
//import static org.mockito.ArgumentMatchers.anyInt;
//import static org.mockito.ArgumentMatchers.argThat;
//import static org.mockito.Mockito.never;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//
///**
// * Test class for PMTemplateStrategyBean focusing on abnormal data template creation
// * Uses ObjectMapper.readValue() to deserialize JSON strings directly into request objects
// */
//class PMTemplateAbnormalDataTest {
//
//    private static final String ABNORMAL_DATA_TEMPLATE_JSON = """
//            {
//                "general_data": {
//                    "id": null,
//                    "description": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA",
//                    "agency_id": 1,
//                    "time_zone": "America/New_York",
//                    "start_time": null,
//                    "metric_type": "abnormal_data",
//                    "week_days": [],
//                    "date_range": {
//                        "scope": "RELATIVE",
//                        "start_date": null,
//                        "end_date": null,
//                        "unit": "WEEK",
//                        "offset": 1,
//                        "target_date": null
//                    },
//                    "aggregation": "HOURLY",
//                    "metadata": {
//                        "bin_size": null,
//                        "from_time": "00:00",
//                        "to_time": "23:59",
//                        "from_date": null,
//                        "to_date": null,
//                        "abnormal_data_filter": [
//                            {
//                                "event": 0,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 1,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 2,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 3,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 4,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 5,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 6,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 7,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 8,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 9,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 10,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 11,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 12,
//                                "params": [],
//                                "deviation": 10
//                            },
//                            {
//                                "event": 20,
//                                "params": [],
//                                "deviation": 10
//                            }
//                        ]
//                    }
//                },
//                "intersection": {
//                    "scope": "ALL_INTERSECTIONS",
//                    "uuids": []
//                },
//                "schedule": {
//                    "scope": "EVERY_DAY",
//                    "value": {
//                        "date": "2025-06-30",
//                        "offset": 1,
//                        "day_of_week": [
//                            "MONDAY",
//                            "TUESDAY",
//                            "WEDNESDAY",
//                            "THURSDAY",
//                            "FRIDAY",
//                            "SATURDAY",
//                            "SUNDAY"
//                        ],
//                        "day_of_month": [
//                            1
//                        ]
//                    },
//                    "mail_receive": true,
//                    "time": "07:00"
//                }
//            }
//            """;
//
//    private static final String INVALID_EVENT_CODE_JSON = """
//            {
//                "general_data": {
//                    "id": null,
//                    "description": "Test Template with Invalid Event Code",
//                    "agency_id": 1,
//                    "time_zone": "America/New_York",
//                    "metric_type": "abnormal_data",
//                    "week_days": [],
//                    "date_range": {
//                        "scope": "RELATIVE",
//                        "unit": "WEEK",
//                        "offset": 1
//                    },
//                    "metadata": {
//                        "from_time": "00:00",
//                        "to_time": "23:59",
//                        "abnormal_data_filter": [
//                            {
//                                "event": 999,
//                                "params": [],
//                                "deviation": 10
//                            }
//                        ]
//                    }
//                },
//                "intersection": {
//                    "scope": "ALL_INTERSECTIONS",
//                    "uuids": []
//                },
//                "schedule": {
//                    "scope": "EVERY_DAY",
//                    "value": {
//                        "date": "2025-06-30",
//                        "offset": 1,
//                        "day_of_week": ["MONDAY"],
//                        "day_of_month": [1]
//                    },
//                    "mail_receive": true,
//                    "time": "07:00"
//                }
//            }
//            """;
//
//    private static final String NEGATIVE_DEVIATION_JSON = """
//            {
//                "general_data": {
//                    "id": null,
//                    "description": "Test Template with Negative Deviation",
//                    "agency_id": 1,
//                    "time_zone": "America/New_York",
//                    "metric_type": "abnormal_data",
//                    "week_days": [],
//                    "date_range": {
//                        "scope": "RELATIVE",
//                        "unit": "WEEK",
//                        "offset": 1
//                    },
//                    "metadata": {
//                        "from_time": "00:00",
//                        "to_time": "23:59",
//                        "abnormal_data_filter": [
//                            {
//                                "event": 0,
//                                "params": [],
//                                "deviation": -5
//                            }
//                        ]
//                    }
//                },
//                "intersection": {
//                    "scope": "ALL_INTERSECTIONS",
//                    "uuids": []
//                },
//                "schedule": {
//                    "scope": "EVERY_DAY",
//                    "value": {
//                        "date": "2025-06-30",
//                        "offset": 1,
//                        "day_of_week": ["MONDAY"],
//                        "day_of_month": [1]
//                    },
//                    "mail_receive": true,
//                    "time": "07:00"
//                }
//            }
//            """;
//
//    private static final String NULL_TIMEZONE_JSON = """
//            {
//                "general_data": {
//                    "id": null,
//                    "description": "Test Template with Null Timezone",
//                    "agency_id": 1,
//                    "time_zone": null,
//                    "metric_type": "abnormal_data",
//                    "week_days": [],
//                    "date_range": {
//                        "scope": "RELATIVE",
//                        "unit": "WEEK",
//                        "offset": 1
//                    },
//                    "metadata": {
//                        "from_time": "00:00",
//                        "to_time": "23:59",
//                        "abnormal_data_filter": [
//                            {
//                                "event": 0,
//                                "params": [],
//                                "deviation": 10
//                            }
//                        ]
//                    }
//                },
//                "intersection": {
//                    "scope": "ALL_INTERSECTIONS",
//                    "uuids": []
//                },
//                "schedule": {
//                    "scope": "EVERY_DAY",
//                    "value": {
//                        "date": "2025-06-30",
//                        "offset": 1,
//                        "day_of_week": ["MONDAY"],
//                        "day_of_month": [1]
//                    },
//                    "mail_receive": true,
//                    "time": "07:00"
//                }
//            }
//            """;
//
//    private static final String EMPTY_TIMEZONE_JSON = """
//            {
//                "general_data": {
//                    "id": null,
//                    "description": "Test Template with Empty Timezone",
//                    "agency_id": 1,
//                    "time_zone": "",
//                    "metric_type": "abnormal_data",
//                    "week_days": [],
//                    "date_range": {
//                        "scope": "RELATIVE",
//                        "unit": "WEEK",
//                        "offset": 1
//                    },
//                    "metadata": {
//                        "from_time": "00:00",
//                        "to_time": "23:59",
//                        "abnormal_data_filter": [
//                            {
//                                "event": 0,
//                                "params": [],
//                                "deviation": 10
//                            }
//                        ]
//                    }
//                },
//                "intersection": {
//                    "scope": "ALL_INTERSECTIONS",
//                    "uuids": []
//                },
//                "schedule": {
//                    "scope": "EVERY_DAY",
//                    "value": {
//                        "date": "2025-06-30",
//                        "offset": 1,
//                        "day_of_week": ["MONDAY"],
//                        "day_of_month": [1]
//                    },
//                    "mail_receive": true,
//                    "time": "07:00"
//                }
//            }
//            """;
//
//    private static final String UPDATE_GENERAL_DATA_JSON = """
//            {
//                "id": 535,
//                "description": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA1111",
//                "agency_id": 1738,
//                "time_zone": "America/New_York",
//                "start_time": null,
//                "metric_type": "abnormal_data",
//                "week_days": [],
//                "date_range": {
//                    "scope": "RELATIVE",
//                    "unit": "WEEK",
//                    "offset": 1,
//                    "start_date": null,
//                    "end_date": null
//                },
//                "aggregation": "HOURLY",
//                "metadata": {
//                    "bin_size": "null",
//                    "from_time": "00:00:00",
//                    "to_time": "23:59:00",
//                    "abnormal_data_filter": [
//                        {
//                            "event": 0,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 1,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 2,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 3,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 4,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 5,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 6,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 7,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 8,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 9,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 10,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 11,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 12,
//                            "params": [],
//                            "deviation": 10
//                        },
//                        {
//                            "event": 20,
//                            "params": [],
//                            "deviation": 10
//                        }
//                    ]
//                },
//                "owner_id": 21,
//                "metric_type_label": "Abnormal Data",
//                "status": "ACTIVE",
//                "owner": {
//                    "id": 21,
//                    "name": "Family, Insights",
//                    "email": "<EMAIL>"
//                },
//                "created_at": "2025-06-29T23:43:25.603+00:00"
//            }
//            """;
//
//    private static final String UPDATE_GENERAL_DATA_NULL_TIMEZONE_JSON = """
//            {
//                "id": 535,
//                "description": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA1111",
//                "agency_id": 1738,
//                "time_zone": null,
//                "start_time": null,
//                "metric_type": "abnormal_data",
//                "week_days": [],
//                "date_range": {
//                    "scope": "RELATIVE",
//                    "unit": "WEEK",
//                    "offset": 1,
//                    "start_date": null,
//                    "end_date": null
//                },
//                "aggregation": "HOURLY",
//                "metadata": {
//                    "bin_size": "null",
//                    "from_time": "00:00:00",
//                    "to_time": "23:59:00",
//                    "abnormal_data_filter": [
//                        {
//                            "event": 0,
//                            "params": [],
//                            "deviation": 10
//                        }
//                    ]
//                },
//                "owner_id": 21,
//                "metric_type_label": "Abnormal Data",
//                "status": "ACTIVE",
//                "owner": {
//                    "id": 21,
//                    "name": "Family, Insights",
//                    "email": "<EMAIL>"
//                },
//                "created_at": "2025-06-29T23:43:25.603+00:00"
//            }
//            """;
//
//    private static final String UPDATE_GENERAL_DATA_EMPTY_TIMEZONE_JSON = """
//            {
//                "id": 535,
//                "description": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA1111",
//                "agency_id": 1738,
//                "time_zone": "",
//                "start_time": null,
//                "metric_type": "abnormal_data",
//                "week_days": [],
//                "date_range": {
//                    "scope": "RELATIVE",
//                    "unit": "WEEK",
//                    "offset": 1,
//                    "start_date": null,
//                    "end_date": null
//                },
//                "aggregation": "HOURLY",
//                "metadata": {
//                    "bin_size": "null",
//                    "from_time": "00:00:00",
//                    "to_time": "23:59:00",
//                    "abnormal_data_filter": [
//                        {
//                            "event": 0,
//                            "params": [],
//                            "deviation": 10
//                        }
//                    ]
//                },
//                "owner_id": 21,
//                "metric_type_label": "Abnormal Data",
//                "status": "ACTIVE",
//                "owner": {
//                    "id": 21,
//                    "name": "Family, Insights",
//                    "email": "<EMAIL>"
//                },
//                "created_at": "2025-06-29T23:43:25.603+00:00"
//            }
//            """;
//
//    @InjectMocks
//    private PMTemplateStrategyBean bean;
//
//    // Strategy
//    @Mock
//    private AgencyStrategy agencyStrategy;
//
//    @Mock
//    private StudioUserService studioUserService;
//
//    @Mock
//    private IntersectionStrategy intersectionStrategy;
//
//    @Mock
//    private ReportNotificationStrategy reportNotificationStrategy;
//
//    @Mock
//    private PMTemplateRepository pmTemplateRepository;
//
//    @Mock
//    private PMCalculator pmCalculator;
//
//    @Mock
//    private TaskProgressRetriever taskProgressRetriever;
//
//    // Repository
//    @Mock
//    private TemplateScheduleRepository scheduleRepository;
//
//    @Mock
//    private PMResultRepository pmResultRepository;
//
//    @Mock
//    private PMResultSharedDataRepository pmResultSharedDataRepository;
//
//    @Mock
//    private TimezoneFactory timezoneFactory;
//
//    private final MockedStatic<BeanFinder> beanFinderMocked = Mockito.mockStatic(BeanFinder.class);
//    private ObjectMapper objectMapper;
//
//    @BeforeEach
//    void beforeEach() {
//        MockitoAnnotations.openMocks(this);
//        objectMapper = BeanFinderMocker.objectMapper();
//        beanFinderMocked.when(BeanFinder::getDefaultObjectMapper).thenReturn(objectMapper);
//        beanFinderMocked.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
//    }
//
//    @AfterEach
//    void afterEach() {
//        beanFinderMocked.close();
//    }
//
//
//    @Test
//    void testCreate_abnormalDataTemplateWithNullTimezone_success() throws Exception {
//        // Given: Create request from JSON string with null timezone
//        PMTemplateCreateRequestVO requestVO = objectMapper.readValue(NULL_TIMEZONE_JSON, PMTemplateCreateRequestVO.class);
//        requestVO.getGeneralDataVO().setTimezoneId("America/New_York");
//        requestVO.getGeneralDataVO().setTimeZone(null);
//        // Mock studio user service
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock agency strategy for intersection verification
//        Mockito.when(agencyStrategy.verifyIntersectionsInAgency(anyInt(), anyCollection()))
//                .thenReturn(IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS);
//
//        // Mock timezone factory to return a default timezone when null is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone(any(), any()))
//                .thenReturn("America/New_York");
//
//        // When: Create template
//        PMTemplateDetailResultObject resultObject = bean.create(requestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertNotNull(resultObject.getData());
//        assertEquals(PMTemplateDetailResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify template data
//        PMTemplateDetailVO templateDetail = resultObject.getData();
//        assertNotNull(templateDetail.getGeneralDataVO());
//        assertEquals("Test Template with Null Timezone", templateDetail.getGeneralDataVO().getDescription());
//        assertEquals("abnormal_data", templateDetail.getGeneralDataVO().getMetricId());
//
//        // Verify that timezone was resolved by TimezoneFactory
//        assertEquals("America/New_York", templateDetail.getGeneralDataVO().getTimezoneId());
//
//        // Verify that TimezoneFactory was called with null timezone and agency ID
//    }
//
//    @Test
//    void testCreate_abnormalDataTemplateWithEmptyTimezone_success() throws Exception {
//        // Given: Create request from JSON string with empty timezone
//        PMTemplateCreateRequestVO requestVO = objectMapper.readValue(EMPTY_TIMEZONE_JSON, PMTemplateCreateRequestVO.class);
//        requestVO.getGeneralDataVO().setTimezoneId("America/New_York");
//        requestVO.getGeneralDataVO().setTimeZone(null);
//        // Mock studio user service
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock agency strategy for intersection verification
//        Mockito.when(agencyStrategy.verifyIntersectionsInAgency(anyInt(), anyCollection()))
//                .thenReturn(IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS);
//
//        // Mock timezone factory to return a default timezone when empty string is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone(any(), 1))
//                .thenReturn("America/Chicago");
//
//        // When: Create template
//        PMTemplateDetailResultObject resultObject = bean.create(requestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertNotNull(resultObject.getData());
//        assertEquals(PMTemplateDetailResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify template data
//        PMTemplateDetailVO templateDetail = resultObject.getData();
//        assertNotNull(templateDetail.getGeneralDataVO());
//        assertEquals("Test Template with Empty Timezone", templateDetail.getGeneralDataVO().getDescription());
//        assertEquals("abnormal_data", templateDetail.getGeneralDataVO().getMetricId());
//
//        // Verify that timezone was resolved by TimezoneFactory
//        assertEquals("America/Chicago", templateDetail.getGeneralDataVO().getTimezoneId());
//
//        // Verify that TimezoneFactory was called with empty timezone and agency ID
//    }
//
//    @Test
//    void testCreate_abnormalDataTemplateWithNullTimezoneAndTimezoneFactoryFailure_failure() throws Exception {
//        // Given: Create request from JSON string with null timezone
//        PMTemplateCreateRequestVO requestVO = objectMapper.readValue(NULL_TIMEZONE_JSON, PMTemplateCreateRequestVO.class);
//        requestVO.getGeneralDataVO().setTimezoneId("America/New_York");
//        requestVO.getGeneralDataVO().setTimeZone(null);
//        // Mock studio user service
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock agency strategy for intersection verification
//        Mockito.when(agencyStrategy.verifyIntersectionsInAgency(anyInt(), anyCollection()))
//                .thenReturn(IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS);
//
//        // Mock timezone factory to throw an exception when null timezone is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone(any(), any()))
//                .thenThrow(new IllegalArgumentException("Invalid timezone"));
//
//        assertThrows(IllegalArgumentException.class, ()->bean.create(requestVO));
//
//    }
//
//    @Test
//    void testCreate_abnormalDataTemplateWithValidTimezone_noTimezoneFactoryCall() throws Exception {
//        // Given: Create request from JSON string with valid timezone
//        PMTemplateCreateRequestVO requestVO = objectMapper.readValue(ABNORMAL_DATA_TEMPLATE_JSON, PMTemplateCreateRequestVO.class);
//        requestVO.getGeneralDataVO().setTimezoneId("America/New_York");
//        requestVO.getGeneralDataVO().setTimeZone(null);
//        // Mock studio user service
//        Mockito.when(studioUserService.findByEmail(any())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock agency strategy for intersection verification
//        Mockito.when(agencyStrategy.verifyIntersectionsInAgency(anyInt(), anyCollection()))
//                .thenReturn(IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS);
//
//        // When: Create template
//        PMTemplateDetailResultObject resultObject = bean.create(requestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertNotNull(resultObject.getData());
//        assertEquals(PMTemplateDetailResultObject.StatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify template data
//        PMTemplateDetailVO templateDetail = resultObject.getData();
//        assertNotNull(templateDetail.getGeneralDataVO());
//        assertEquals("America/New_York", templateDetail.getGeneralDataVO().getTimezoneId());
//
//        // Verify that TimezoneFactory was NOT called since timezone was already provided
//       }
//
//    @Test
//    void testUpdateGeneralData_withValidTimezone_success() throws Exception {
//        // Given: Create update request from JSON string with valid timezone
//        PMTemplateSimpleDataVO updateRequestVO = objectMapper.readValue(UPDATE_GENERAL_DATA_JSON, PMTemplateSimpleDataVO.class);
//        updateRequestVO.setTimezoneId("America/New_York");
//        updateRequestVO.setTimeZone(null);
//        Long templateId = 535L;
//
//        // Mock existing template
//        PMTemplate existingTemplate = mockExistingTemplate();
//        Mockito.when(pmTemplateRepository.findById(templateId)).thenReturn(Optional.of(existingTemplate));
//
//        // Mock studio user service for notification
//        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));
//
//        // When: Update general data
//        PMTemplateManipulateResultObject resultObject = bean.updateGeneralData(templateId, updateRequestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertEquals(PMTemplateManipulateStatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify that template was updated with correct timezone
//        verify(pmTemplateRepository, times(1)).save(argThat(template ->
//            "America/New_York".equals(template.getTimezoneId())
//        ));
//
//        // Verify that TimezoneFactory was NOT called since valid timezone was provided
//        // Verify notification was sent
//    }
//
//    @Test
//    void testUpdateGeneralData_withNullTimezone_success() throws Exception {
//        // Given: Create update request from JSON string with null timezone
//        PMTemplateSimpleDataVO updateRequestVO = objectMapper.readValue(UPDATE_GENERAL_DATA_NULL_TIMEZONE_JSON, PMTemplateSimpleDataVO.class);
//        updateRequestVO.setTimezoneId("America/New_York");
//        updateRequestVO.setTimeZone(null);
//        Long templateId = 535L;
//
//        // Mock existing template
//        PMTemplate existingTemplate = mockExistingTemplate();
//        Mockito.when(pmTemplateRepository.findById(templateId)).thenReturn(Optional.of(existingTemplate));
//
//        // Mock studio user service for notification
//        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock timezone factory to return a default timezone when null is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone(any(), any()))
//                .thenReturn("America/Chicago");
//
//        // When: Update general data
//        PMTemplateManipulateResultObject resultObject = bean.updateGeneralData(templateId, updateRequestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertEquals(PMTemplateManipulateStatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify that TimezoneFactory was called with null timezone and agency ID
//        verify(pmTemplateRepository, times(1)).save(argThat(template ->
//            "America/Chicago".equals(template.getTimezoneId())
//        ));
//
//        // Verify notification was sent
//    }
//
//    @Test
//    void testUpdateGeneralData_withEmptyTimezone_success() throws Exception {
//        // Given: Create update request from JSON string with empty timezone
//        PMTemplateSimpleDataVO updateRequestVO = objectMapper.readValue(UPDATE_GENERAL_DATA_EMPTY_TIMEZONE_JSON, PMTemplateSimpleDataVO.class);
//        updateRequestVO.setTimezoneId("America/New_York");
//        updateRequestVO.setTimeZone(null);
//        Long templateId = 535L;
//
//        // Mock existing template
//        PMTemplate existingTemplate = mockExistingTemplate();
//        Mockito.when(pmTemplateRepository.findById(templateId)).thenReturn(Optional.of(existingTemplate));
//
//        // Mock studio user service for notification
//        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock timezone factory to return a default timezone when empty string is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone("Ame", 1738))
//                .thenReturn("America/Los_Angeles");
//
//        // When: Update general data
//        PMTemplateManipulateResultObject resultObject = bean.updateGeneralData(templateId, updateRequestVO);
//
//        // Then: Verify success
//        assertNotNull(resultObject);
//        assertEquals(PMTemplateManipulateStatusCode.SUCCESS, resultObject.getStatusCode());
//
//        // Verify that TimezoneFactory was called with empty timezone and agency ID
//        // Verify that template was updated with resolved timezone
//        verify(pmTemplateRepository, times(1)).save(argThat(template ->
//            "America/Los_Angeles".equals(template.getTimezoneId())
//        ));
//
//        // Verify notification was sent
//    }
//
//    @Test
//    void testUpdateGeneralData_withNullTimezoneAndTimezoneFactoryFailure_failure() throws Exception {
//        // Given: Create update request from JSON string with null timezone
//        PMTemplateSimpleDataVO updateRequestVO = objectMapper.readValue(UPDATE_GENERAL_DATA_NULL_TIMEZONE_JSON, PMTemplateSimpleDataVO.class);
//        updateRequestVO.setTimezoneId("America/New_York");
//        updateRequestVO.setTimeZone(null);
//        Long templateId = 535L;
//
//        // Mock existing template
//        PMTemplate existingTemplate = mockExistingTemplate();
//        Mockito.when(pmTemplateRepository.findById(templateId)).thenReturn(Optional.of(existingTemplate));
//
//        // Mock studio user service for notification
//        Mockito.when(studioUserService.findById(anyInt())).thenReturn(Optional.of(mockStudioUser()));
//
//        // Mock timezone factory to throw an exception when null timezone is provided
//        Mockito.when(timezoneFactory.validateAndResolveTimezone(any(), 1738))
//                .thenThrow(new IllegalArgumentException("Invalid timezone"));
//
//
//        // Then: Verify failure due to timezone resolution error
//        assertThrows(IllegalArgumentException.class, ()->bean.updateGeneralData(templateId, updateRequestVO));
//
//    }
//
//    @Test
//    void testUpdateGeneralData_templateNotFound_failure() throws Exception {
//        // Given: Create update request from JSON string
//        PMTemplateSimpleDataVO updateRequestVO = objectMapper.readValue(UPDATE_GENERAL_DATA_JSON, PMTemplateSimpleDataVO.class);
//        updateRequestVO.setTimezoneId("America/New_York");
//        updateRequestVO.setTimeZone(null);
//        Long templateId = 999L; // Non-existent template ID
//
//        // Mock template not found
//        Mockito.when(pmTemplateRepository.findById(templateId)).thenReturn(Optional.empty());
//
//        // When: Update general data
//        PMTemplateManipulateResultObject resultObject = bean.updateGeneralData(templateId, updateRequestVO);
//
//        // Then: Verify failure
//        assertNotNull(resultObject);
//        assertEquals(PMTemplateManipulateStatusCode.NOT_FOUND, resultObject.getStatusCode());
//
//        // Verify that TimezoneFactory was NOT called
//        verify(timezoneFactory, never()).validateAndResolveTimezone(any(), anyInt());
//
//        // Verify that template was NOT saved
//        verify(pmTemplateRepository, never()).save(any());
//
//        // Verify notification was NOT sent
//    }
//
//
//    /**
//     * Creates a mock existing PMTemplate for update tests
//     */
//    private PMTemplate mockExistingTemplate() {
//        return PMTemplate.builder()
//                .id(535L)
//                .description("Original Description")
//                .agencyId(1738)
//                .timezone("America/New_York")
//                .metricType(AnalysisType.ABNORMAL_DATA)
//                .dateRange(DateRange.builder()
//                        .scope(DateRange.Scope.RELATIVE)
//                        .unit(DateRange.Unit.WEEK)
//                        .offset(1)
//                        .build())
//                .metadata(PMMetadata.builder()
//                        .fromTime(LocalTime.of(0, 0))
//                        .toTime(LocalTime.of(23, 59))
//                        .abnormalEventFilters(new ArrayList<>())
//                        .build())
//                .ownerId(21L)
//                .status(TemplateStatus.ACTIVE)
//                .deleted(false)
//                .intersectionList("ALL_INTERSECTIONS")
//                .schedule(TemplateSchedule.builder()
//                        .scope(ScheduleScope.EVERY_DAY)
//                        .build())
//                .build();
//    }
//
//    /**
//     * Creates a mock StudioUserDto for testing
//     */
//    private StudioUserDto mockStudioUser() {
//        StudioUserDto studioUserDto = new StudioUserDto();
//        studioUserDto.setId(1);
//        studioUserDto.setIsEnabled(true);
//        studioUserDto.setName("Test User");
//        studioUserDto.setEmail("<EMAIL>");
//        return studioUserDto;
//    }
//}