package com.siemens.spm.analysis.strategy.intercom;

import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportInfoVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateDetailVO;
import com.siemens.spm.analysis.config.IntercomConfig;
import com.siemens.spm.analysis.utils.BeanFinderMocker;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.notification.config.MailConfig;
import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.service.NotificationSenderService;
import com.siemens.spm.notification.vo.EmailMessageVO;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.context.MessageSource;
import org.springframework.web.client.RestTemplate;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.wildfly.common.Assert;

import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class ReportNotificationStrategyBeanTest {

    @InjectMocks
    private ReportNotificationStrategyBean reportNotificationStrategyBean;

    @Mock
    private IntercomConfig intercomConfig;

    @Mock
    private MailConfig mailConfig;

    @Mock
    private SpringTemplateEngine templateEngine;

    @Mock
    private MessageSource messageSource;

    @Mock
    private NotificationSenderService notificationSenderService;

    @Mock
    private StudioAgencyService studioAgencyService;

    @Captor
    private ArgumentCaptor<Context> contextCaptor;

    @Mock
    private RestTemplate restTemplate;

    private static MockedStatic<BeanFinder> beanFinderMock;

    private static final int AGENCY_ID = 1;

    @BeforeAll
    static void initClass() {
        beanFinderMock = Mockito.mockStatic(BeanFinder.class);
        beanFinderMock.when(BeanFinder::getDefaultMessageService).thenReturn(BeanFinderMocker.messageService());
    }

    @AfterAll
    static void afterTestClass() {
        beanFinderMock.close();
    }

    @BeforeEach
    void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendReportTemplateNotiAsync_caseCreate() {
        reportNotificationStrategyBean
                .sendSummaryTemplateNotiAsync(mockReportTemplateDetailVO(),
                        Collections.singletonList(mockStudioUser()),
                        TemplateConstant.ActionOnTemplate.CREATED);

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
    }

    @Test
    void testSendReportTemplateNotiAsync_caseActivate() {
        reportNotificationStrategyBean
                .sendSummaryTemplateNotiAsync(mockReportTemplateDetailVO(),
                        Collections.singletonList(mockStudioUser()),
                        TemplateConstant.ActionOnTemplate.ACTIVATE);

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
    }

    @Test
    void testSendReportTemplateNotiAsync_caseDeactivate() {
        reportNotificationStrategyBean
                .sendSummaryTemplateNotiAsync(mockReportTemplateDetailVO(),
                        Collections.singletonList(mockStudioUser()),
                        TemplateConstant.ActionOnTemplate.DEACTIVATE);

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
    }

    @Test
    void testSendReportTemplateNotiAsync_caseUpdate() {
        reportNotificationStrategyBean
                .sendSummaryTemplateNotiAsync(mockReportTemplateDetailVO(),
                        Collections.singletonList(mockStudioUser()),
                        TemplateConstant.ActionOnTemplate.UPDATE);

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
    }

    @Test
    void testSendReportTemplateNotiAsync_caseDelete() {
        reportNotificationStrategyBean
                .sendSummaryTemplateNotiAsync(mockReportTemplateDetailVO(),
                        Collections.singletonList(mockStudioUser()),
                        TemplateConstant.ActionOnTemplate.DELETE);

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
    }

    @Test
    void testSendReportResultNotiAsync() throws NotificationSenderException {
        Mockito.when(templateEngine.process(anyString(), any())).thenReturn("Content");
        Mockito.when(messageSource.getMessage(anyString(), any(), any())).thenReturn("Subject");

        reportNotificationStrategyBean
                .sendSummaryResultNotiAsync(mockSummaryReportInfoVO(),
                        Collections.singletonList(mockStudioUser()),
                        ActionVO.builder()
                                .type(ActionType.OPEN_LINK)
                                .target(ActionTarget.NOTIFICATIONS)
                                .build());

        Mockito.verify(intercomConfig, times(1)).getUserServiceEndpoint();
        Mockito.verify(mailConfig, times(1)).getSenderEmail();
        Mockito.verify(notificationSenderService, times(1)).sendMessage(any());
    }

//    @Test
//    void testSendSummaryResultNotiAsync_WithTimezone() throws NotificationSenderException, StudioException {
//        // Given
//        String timezone = "America/New_York";
//        LocalDateTime createdAt = LocalDateTime.of(2024, 3, 15, 14, 30, 0); // 2:30 PM
//
//        SummaryReportInfoVO reportInfoVO = new SummaryReportInfoVO();
//        reportInfoVO.setId(1L);
//        reportInfoVO.setAgencyId(123);
//        reportInfoVO.setName("Test Report");
//        reportInfoVO.setTimeZone(timezone);
//        reportInfoVO.setCreatedAt(Timestamp.valueOf(createdAt));
//        reportInfoVO.setFromDate(LocalDate.parse("2024-03-01"));
//        reportInfoVO.setToDate(LocalDate.parse("2024-03-15"));
//        reportInfoVO.setFromTime(LocalTime.parse("09:00"));
//        reportInfoVO.setToTime(LocalTime.parse("17:00"));
//        reportInfoVO.setWeekDays(Set.of(DayOfWeek.MONDAY, DayOfWeek.WEDNESDAY, DayOfWeek.FRIDAY));
//        reportInfoVO.setNumberOfIntersections(5);
//
//        StudioUserDto user = new StudioUserDto();
//        user.setId(1);
//        user.setEmail("<EMAIL>");
//
//        StudioAgencyDto agencyDto = new StudioAgencyDto();
//        agencyDto.setAgencyName("Test Agency");
//
//        when(studioAgencyService.getAgencyById(123)).thenReturn(java.util.Optional.of(agencyDto));
//        when(mailConfig.getSenderEmail()).thenReturn("<EMAIL>");
//        when(messageSource.getMessage(anyString(), any(), any())).thenReturn("Test Subject");
////        when(restTemplate.exchange( any(),any(),any())).thenReturn(Mockito.any())
//        ActionVO actionVO = ActionVO.builder()
//                .type(ActionType.OPEN_LINK)
//                .target(ActionTarget.NOTIFICATIONS)
//                .build();
//
//        // When
//        reportNotificationStrategyBean.sendSummaryResultNotiAsync(reportInfoVO, Collections.singletonList(user), actionVO);
//
//        // Then
//        verify(templateEngine).process(eq("summary_result_created"), contextCaptor.capture());
//        Context capturedContext = contextCaptor.getValue();
//
//        // Verify that the createdAt time is correctly formatted in the specified timezone
//        String formattedCreatedAt = (String) capturedContext.getVariable("createdAt");
//        Assert.assertTrue(formattedCreatedAt.contains("2:30 PM")
//        );
//        Assert.assertTrue(formattedCreatedAt.contains("EDT") || formattedCreatedAt.contains("EST")
//        );
//
//        // Verify email was sent with correct content
//        ArgumentCaptor<EmailMessageVO> emailCaptor = ArgumentCaptor.forClass(EmailMessageVO.class);
//        verify(notificationSenderService).sendMessage(emailCaptor.capture());
//
//        EmailMessageVO capturedEmail = emailCaptor.getValue();
//        Assertions.assertEquals("<EMAIL>", capturedEmail.getSender());
//        Assertions.assertEquals(Collections.singletonList("<EMAIL>"), capturedEmail.getToRecipients());
//        Assertions.assertEquals("Test Subject", capturedEmail.getSubject());
//        Assertions.assertEquals("Test email content", capturedEmail.getContent());
//    }

    private SummaryTemplateDetailVO mockReportTemplateDetailVO() {
        return SummaryTemplateDetailVO.builder()
                .agencyId(AGENCY_ID)
                .name("Foo")
                .description("Bar")
                .build();
    }

    private SummaryReportInfoVO mockSummaryReportInfoVO() {
        return SummaryReportInfoVO.builder()
                .createdAt(new Timestamp(System.currentTimeMillis()))
                .weekDays(new HashSet<>(Collections.singleton(DayOfWeek.MONDAY)))
                .build();
    }

    private StudioUserDto mockStudioUser() {
        StudioUserDto studioUserDto = new StudioUserDto();
        studioUserDto.setId(1);
        studioUserDto.setIsEnabled(true);
        studioUserDto.setName("Foo");
        studioUserDto.setEmail("<EMAIL>");
        return studioUserDto;
    }

}
