package com.siemens.spm.analysis.summaryreport.template.strategy;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateDeleteRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateManipulateResultObject.SummaryTemplateManipulateStatusCode;
import com.siemens.spm.analysis.api.vo.response.summaryreport.SummaryTemplateSearchResultObject;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryMetricResponseVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportInfoVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateCoreDataVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateDetailVO;
import com.siemens.spm.analysis.config.ReportTemplateConfig;
import com.siemens.spm.analysis.domain.Phase;
import com.siemens.spm.analysis.domain.ReportResult;
import com.siemens.spm.analysis.domain.ReportResultStatistic;
import com.siemens.spm.analysis.domain.ReportTemplate;
import com.siemens.spm.analysis.domain.StatisticBlock;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.analysis.factory.TimezoneFactory;
import com.siemens.spm.analysis.repository.IntersectionConfigRepository;
import com.siemens.spm.analysis.repository.PhaseStatRepository;
import com.siemens.spm.analysis.repository.ReportResultRepository;
import com.siemens.spm.analysis.repository.ReportResultStatisticRepository;
import com.siemens.spm.analysis.repository.ReportTemplateRepository;
import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
import com.siemens.spm.analysis.repository.filterdata.ReportTemplateFilterData;
import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
import com.siemens.spm.analysis.summaryreport.result.util.ReportResultBuilder;
import com.siemens.spm.analysis.summaryreport.template.strategy.exception.ProcessReportTemplateException;
import com.siemens.spm.analysis.summaryreport.template.strategy.exception.ReportTemplateNotFoundException;
import com.siemens.spm.analysis.summaryreport.template.util.ReportMetricBuilder;
import com.siemens.spm.analysis.summaryreport.template.util.ReportTemplateSearchHelper;
import com.siemens.spm.analysis.summaryreport.template.util.SummaryTemplateBuilder;
import com.siemens.spm.analysis.summaryreport.template.util.TemplateScheduleBuilder;
import com.siemens.spm.analysis.summaryreport.util.TemplateScheduleHelper;
import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.IntersectionScope;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.IntersectionSearchResponseDataVO;
import com.siemens.spm.common.shared.vo.ReportResultActionDataVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.common.util.NameUtil;
import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.persistence.IntersectionConfig;
import com.siemens.spm.perflog.persistence.PhaseStat;
import com.siemens.spm.perflog.repository.filterdata.PhaseStatFilterDataVO;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR> Nguyen
 */
@Slf4j
@Service
public class SummaryTemplateStrategyBean implements SummaryTemplateStrategy {

    private static final String TEMPLATE_NOT_FOUND_MESSAGE = "The report template %s was not found!";
    private static final String STUDIO_USER_ERROR_MSG = "Error when get user by id from Studio";

    private final ReportTemplateConfig reportTemplateConfig;
    private final IntersectionStrategy intersectionStrategy;
    private final ReportNotificationStrategy reportNotificationStrategy;
    private final SummaryStatisticStrategy summaryStatisticStrategy;
    private final TaskProgressRetriever taskProgressRetriever;
    private final StudioUserService studioUserService;
    private final StudioAgencyService studioAgencyService;
    private final ReportTemplateRepository reportTemplateRepository;
    private final TemplateScheduleRepository templateScheduleRepository;
    private final ReportResultRepository reportResultRepository;
    private final ReportResultStatisticRepository reportResultStatisticRepository;
    private final PhaseStatRepository phaseStatRepository;
    private final IntersectionConfigRepository intConfigRepo;
    private final TimezoneFactory timezoneFactory;

    public SummaryTemplateStrategyBean(ReportTemplateConfig reportTemplateConfig,
                                       IntersectionStrategy intersectionStrategy,
                                       ReportNotificationStrategy reportNotificationStrategy,
                                       SummaryStatisticStrategy summaryStatisticStrategy,
                                       TaskProgressRetriever taskProgressRetriever,
                                       StudioUserService studioUserService,
                                       StudioAgencyService studioAgencyService,
                                       ReportTemplateRepository reportTemplateRepository,
                                       TemplateScheduleRepository templateScheduleRepository,
                                       ReportResultRepository reportResultRepository,
                                       ReportResultStatisticRepository reportResultStatisticRepository,
                                       PhaseStatRepository phaseStatRepository,
                                       IntersectionConfigRepository intConfigRepo,
                                       TimezoneFactory timezoneFactory) {
        this.reportTemplateConfig = reportTemplateConfig;
        this.intersectionStrategy = intersectionStrategy;
        this.reportNotificationStrategy = reportNotificationStrategy;
        this.summaryStatisticStrategy = summaryStatisticStrategy;
        this.taskProgressRetriever = taskProgressRetriever;
        this.studioUserService = studioUserService;
        this.studioAgencyService = studioAgencyService;
        this.reportTemplateRepository = reportTemplateRepository;
        this.templateScheduleRepository = templateScheduleRepository;
        this.reportResultRepository = reportResultRepository;
        this.reportResultStatisticRepository = reportResultStatisticRepository;
        this.phaseStatRepository = phaseStatRepository;
        this.intConfigRepo = intConfigRepo;
        this.timezoneFactory = timezoneFactory;
    }

    /**
     * {@inheritDoc}
     */
    @Transactional
    @Override
    public SummaryTemplateDetailResultObject createReportTemplate(SummaryTemplateCreateRequestVO requestVO) {
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error("Error when get current user", e);
            return new SummaryTemplateDetailResultObject(SummaryTemplateDetailResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn("Cannot find current user with email={} in Studio", email);
            return new SummaryTemplateDetailResultObject(SummaryTemplateDetailResultObject.StatusCode.UNAUTHORIZED);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        // Verify agency
        Integer agencyId = requestVO.getAgencyId();
        if (agencyId == null) {
            log.debug("Missing agency id");
            return new SummaryTemplateDetailResultObject(null,
                    SummaryTemplateDetailResultObject.StatusCode.MISSING_AGENCY_ID);
        }

        // Validate and resolve timezone for template creation
        String timezoneId = requestVO.getTimezoneId();
        if(StringUtils.isEmpty(timezoneId)){
            requestVO.setTimezoneId(timezoneFactory.validateAndResolveTimezone(
                    timezoneId,
                    requestVO.getAgencyId()));
        }
        requestVO.setTimeZone(DateTimeUtils.getZoneOffSet(timezoneId));

        // Everything is ok. Build and save template
        ReportTemplate template = SummaryTemplateBuilder.buildEntityFromCreateRequestVO(requestVO);
        template.setAgencyId(agencyId);
        template.setOwnerId(userId);

        TemplateSchedule schedule = TemplateScheduleBuilder.buildEntityFromVO(requestVO.getScheduleVO());
        templateScheduleRepository.saveAndFlush(schedule);
        template.setSchedule(schedule);
        reportTemplateRepository.save(template);

        // Process build response and send noti
        SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                .buildTemplateDetailVO(template);

        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.CREATED);

        return new SummaryTemplateDetailResultObject(templateDetailVO,
                SummaryTemplateDetailResultObject.StatusCode.CREATED);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateManipulateResultObject softDeleteReportTemplate(SummaryTemplateDeleteRequestVO deleteRequestVO) {
        List<ReportTemplate> templates = reportTemplateRepository.findAllByIdIn(deleteRequestVO.getTemplateIds());
        if (templates.isEmpty()) {
            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.TEMPLATE_NOT_FOUND);
        }

        // Filter template which delete field is false
        List<ReportTemplate> templatesToDelete = templates.stream()
                .filter(template -> !template.isDeleted())
                .toList();

        for (ReportTemplate template : templatesToDelete) {
            template.setDeleted(true);
            reportTemplateRepository.save(template);

            SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                    .buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.DELETE);
        }

        return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateManipulateResultObject addIntersections(Long templateId,
                                                                  IntersectionIdsRequestVO intersectionIdsRequestVO) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);

        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));

            return new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateStatusCode.TEMPLATE_NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        List<String> intersectionIdsListRequest = IntersectionIdsVO
                .resolveIntersectionIdsList(intersectionIdsRequestVO);

        // Verify intersections
        if (!intersectionIdsListRequest.isEmpty()) {
            // TODO
            //            IntersectionsInAgencyVerifyResultObject.StatusCode verifyIntersectionsResult = agencyStrategy
            //                    .verifyIntersectionsInAgency(agencyId, intersectionIdsListRequest);
            //            if (verifyIntersectionsResult != IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS) {
            //                log.debug("Verify intersections in agency failed!");
            //                return new SummaryTemplateManipulateResultObject(
            //                        SummaryTemplateManipulateStatusCode.valueOf(verifyIntersectionsResult.name()));
            //            }
        }

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (IntersectionScope.ALL_INTERSECTIONS.equals(intersectionScope)) {
            template.setIntersectionList(IntersectionConstants.ALL_INTERSECTION_INDICATOR);
            reportTemplateRepository.save(template);

            log.debug("Add intersections to ALL_INTERSECTIONS successful!");

            SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                    .buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.UPDATE);

            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
        } else if (IntersectionScope.SPECIFIC_INTERSECTIONS.equals(intersectionScope)) {
            // Check case add new when current scope is ALL_INTERSECTIONS first
            if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(template.getIntersectionList())) {
                log.debug("Add intersection(s) scope SPECIFIC when current scope is ALL_INTERSECTIONS is illegal!");

                return new SummaryTemplateManipulateResultObject(
                        SummaryTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
            }

            List<String> currentIntersectionIdsList = Arrays.asList(template.getIntersectionList()
                    .split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
            List<String> newIntersectionIdsList = new ArrayList<>(currentIntersectionIdsList);

            for (String uuid : intersectionIdsListRequest) {
                if (!newIntersectionIdsList.contains(uuid)) {
                    newIntersectionIdsList.add(uuid);
                }
            }
            String newIntersectionIdsString = newIntersectionIdsList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

            template.setIntersectionList(newIntersectionIdsString);
            reportTemplateRepository.save(template);

            log.debug("Add new intersection(s) to SPECIFIC_INTERSECTION successful!");

            SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                    .buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.UPDATE);

            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
        } else {
            log.debug("Intersection scope is invalid");

            return new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateManipulateResultObject removeIntersections(Long templateId,
                                                                     IntersectionIdsRequestVO intersectionIdsRequestVO) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));

            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.TEMPLATE_NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (intersectionScope == null) {
            return new SummaryTemplateManipulateResultObject(
                    SummaryTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
        }

        String newIntersectionIdsString;

        switch (intersectionScope) {
            case ALL_INTERSECTIONS -> newIntersectionIdsString = "";
            case SPECIFIC_INTERSECTIONS -> {
                List<String> intersectionIdListRequest = intersectionIdsRequestVO.getUuids();
                if (ListUtil.hasNoItem(intersectionIdListRequest)) {
                    newIntersectionIdsString = template.getIntersectionList();
                } else {
                    newIntersectionIdsString = getAllIntersectionIds(template)
                            .stream()
                            .filter(uuid -> !intersectionIdListRequest.contains(uuid))
                            .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
                }
            }
            default -> newIntersectionIdsString = template.getIntersectionList();
        }

        template.setIntersectionList(newIntersectionIdsString);
        reportTemplateRepository.save(template);

        SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                .buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
    }

    private List<String> getAllIntersectionIds(ReportTemplate template) {
        final String intersectionIdString = Objects.requireNonNull(template.getIntersectionList(),
                "Intersection list is null");
        if (!intersectionIdString.equals(IntersectionConstants.ALL_INTERSECTION_INDICATOR)) {
            return Arrays.asList(intersectionIdString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId())
                .shouldPaginate(false)
                .build();

        return intersectionStrategy.getIntersectionsByFilterInternal(searchRequest)
                .getIntersections()
                .stream()
                .map(IntersectionInternalVO::getId)
                .toList();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateManipulateResultObject activateOrDeactivateReportTemplate(SummaryTemplateActivateRequestVO activeRequestVO) {
        if (activeRequestVO == null || activeRequestVO.getStatus() == null) {
            throw new IllegalArgumentException();
        }

        List<ReportTemplate> templates = reportTemplateRepository.findAllByIdIn(activeRequestVO.getTemplateIds());
        if (templates.isEmpty()) {
            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.TEMPLATE_NOT_FOUND);
        }

        TemplateStatus statusToUpdate = activeRequestVO.getStatus();
        List<ReportTemplate> templatesToUpdateStatus = templates.stream()
                .filter(template -> !template.isDeleted())
                .filter(template -> template.getStatus() != statusToUpdate)
                .toList();

        for (ReportTemplate template : templatesToUpdateStatus) {
            template.setStatus(statusToUpdate);
            reportTemplateRepository.save(template);

            // Build vo to notify user
            TemplateConstant.ActionOnTemplate action = switch (statusToUpdate) {
                case ACTIVE -> TemplateConstant.ActionOnTemplate.ACTIVATE;
                case INACTIVE -> TemplateConstant.ActionOnTemplate.DEACTIVATE;
            };

            SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder.buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(), action);
        }

        return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateCoreDataResultObject getReportTemplateCoreData(Long templateId) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));

            return new SummaryTemplateCoreDataResultObject(null,
                    SummaryTemplateCoreDataResultObject.StatusCode.NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        SummaryTemplateCoreDataVO templateCoreDataVO = SummaryTemplateBuilder.buildCoreDataFromEntity(template);
        updateOwnerData(templateCoreDataVO.getOwnerVO());

        // Ensure timezone is properly resolved for template core data
        String resolvedTimezone = timezoneFactory.validateAndResolveTimezone(
                templateCoreDataVO.getTimeZone(),
                templateCoreDataVO.getAgencyId()
        );
        templateCoreDataVO.setTimeZone(resolvedTimezone);
        log.debug("Successful get report template core data! Template id: {}", templateId);

        return new SummaryTemplateCoreDataResultObject(templateCoreDataVO,
                SummaryTemplateCoreDataResultObject.StatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSearchResultObject searchTemplateInterSections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        // Verify template exist or not
        Long templateId = searchRequestVO.getTemplateId();
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId())
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .shouldPaginate(true)
                .page(searchRequestVO.getPage())
                .size(searchRequestVO.getSize())
                .text(searchRequestVO.getText())
                .status(IntersectionStatus.AVAILABLE.getInsight()) // Just return available intersection
                .build();

        IntersectionSearchResponseDataVO responseDataVO = intersectionStrategy
                .searchIntersectionsInternal(template.getIntersectionList(), searchRequest);

        return IntersectionSearchResultObject.success(responseDataVO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public TemplateScheduleResultObject getReportTemplateScheduleData(Long templateId) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));
            return new TemplateScheduleResultObject(null, TemplateScheduleResultObject.StatusCode.NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        TemplateScheduleVO scheduleVO = TemplateScheduleBuilder.buildVOFromEntity(template.getSchedule());

        log.debug("Get report template schedule data successful! Template id: {}", templateId);

        return TemplateScheduleResultObject.success(scheduleVO);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateSearchResultObject searchReportTemplates(SummaryTemplateSearchRequestVO searchRequestVO) {
        Pair<List<ReportTemplate>, Long> templatesAndCountPair;
        try {
            templatesAndCountPair = getReportTemplatesAndCountFromDB(searchRequestVO);
        } catch (InvalidSortColumnException ex) {
            return new SummaryTemplateSearchResultObject(null,
                    SummaryTemplateSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            return new SummaryTemplateSearchResultObject(null,
                    SummaryTemplateSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<ReportTemplate> templates = templatesAndCountPair.getFirst();
        Long totalCount = templatesAndCountPair.getSecond();

        if (templates.isEmpty()) {
            SummaryTemplateSearchResultObject.ResponseData responseData = SummaryTemplateSearchResultObject.ResponseData
                    .builder()
                    .reportTemplates(new ArrayList<>())
                    .totalCount(totalCount)
                    .build();

            log.debug("Search report templates successful with 0 result! Total count: {}", totalCount);
            return SummaryTemplateSearchResultObject.buildSuccessResponse(responseData);
        }

        List<SummaryTemplateCoreDataVO> templateCoreDataVOs = new ArrayList<>();
        for (ReportTemplate template : templates) {
            SummaryTemplateCoreDataVO templateCoreDataVO = SummaryTemplateBuilder.buildCoreDataFromEntity(template);
            // need refactor call much time to User service
            updateOwnerData(templateCoreDataVO.getOwnerVO());
            templateCoreDataVOs.add(templateCoreDataVO);
        }

        SummaryTemplateSearchResultObject.ResponseData responseData = SummaryTemplateSearchResultObject.ResponseData
                .builder()
                .reportTemplates(templateCoreDataVOs)
                .totalCount(totalCount)
                .build();

        log.debug("Search report templates successful! Total count: {}", totalCount);

        return SummaryTemplateSearchResultObject.buildSuccessResponse(responseData);
    }

    /**
     * {@inheritDoc}
     */
    @Transactional
    @Override
    public SummaryTemplateManipulateResultObject updateReportTemplateCoreData(Long templateId,
                                                                              SummaryTemplateUpdateCoreDataRequestVO updateRequestVO) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));
            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        updateScheduleDataToManualIfNeed(template, updateRequestVO.getDateRange());

        // Validate and resolve timezone with comprehensive fallback strategy
        String timezoneId = updateRequestVO.getTimezoneId();
        if(StringUtils.isEmpty(timezoneId)){
            updateRequestVO.setTimezoneId(timezoneFactory.validateAndResolveTimezone(
                    timezoneId,
                    updateRequestVO.getAgencyId()));
        }
        updateRequestVO.setTimeZone(DateTimeUtils.getZoneOffSet(timezoneId));


        SummaryTemplateBuilder.updateVOToEntity(template, updateRequestVO);
        reportTemplateRepository.save(template);

        log.debug("Update report template core data successful! template_id= {}", templateId);

        SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                .buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
    }

    /**
     * Update schedule data of a specific report template to MANUAL if:
     * <p>
     * 1. Current date range scope is RELATIVE
     * <p>
     * 2. New date range scope is SPECIFIC
     * <p>
     * 3. Current schedule scope is not MANUAL
     *
     * @param template  current report template
     * @param dateRange new date range
     */
    private void updateScheduleDataToManualIfNeed(ReportTemplate template, DateRange dateRange) {
        DateRange currentDateRange;
        try {
            currentDateRange = BeanFinder.getDefaultObjectMapper().readValue(template.getDateRange(), DateRange.class);
        } catch (JsonProcessingException e) {
            log.error("Invalid template: Can't read date range", e);
            throw new IllegalArgumentException("Invalid template: Can't read date range");
        }

        TemplateSchedule templateSchedule = template.getSchedule();
        if (currentDateRange.getScope() == DateRange.Scope.RELATIVE
                && dateRange.getScope() == DateRange.Scope.SPECIFIC
                && templateSchedule.getScope() != ScheduleScope.MANUAL) {
            templateSchedule.setScope(ScheduleScope.MANUAL);
            templateSchedule.setTime(null);
            templateSchedule.setValue(null);
            templateSchedule.setMetricJson(null);

            templateScheduleRepository.save(templateSchedule);
            log.debug("Successful update template schedule to MANUAL, templateId = {}", template.getId());
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SummaryTemplateManipulateResultObject updateReportTemplateScheduleData(Long templateId,
                                                                                  TemplateScheduleVO templateScheduleVO) {
        // Verify template exist or not
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(String.format(TEMPLATE_NOT_FOUND_MESSAGE, templateId));

            return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.NOT_FOUND);
        }

        ReportTemplate template = templateOptional.get();
        TemplateSchedule schedule = template.getSchedule();
        TemplateScheduleBuilder.updateVOToEntity(schedule, templateScheduleVO);

        templateScheduleRepository.save(schedule);

        log.debug("Update report template schedule data successful! template_id= {}", templateId);

        SummaryTemplateDetailVO templateDetailVO = SummaryTemplateBuilder
                .buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return new SummaryTemplateManipulateResultObject(SummaryTemplateManipulateStatusCode.SUCCESS);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public IntersectionSearchResultObject searchAvailableTemplateInterSections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        Integer agencyId;
        Long templateId = searchRequestVO.getTemplateId();

        List<String> currentIntIdsList;
        if (templateId != null) {
            Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
            if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
                return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
            }

            ReportTemplate template = templateOptional.get();
            agencyId = template.getAgencyId();
            String currentIntIdsString = template.getIntersectionList();
            if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(currentIntIdsString)) {
                String[] intIdsArr = currentIntIdsString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
                currentIntIdsList = Arrays.asList(intIdsArr);
            } else {
                IntersectionSearchResponseDataVO responseDataVO = IntersectionSearchResponseDataVO.builder()
                        .intersections(new ArrayList<>())
                        .totalCount(0L)
                        .scope(IntersectionScope.SPECIFIC_INTERSECTIONS)
                        .build();

                return IntersectionSearchResultObject.success(responseDataVO);
            }
        } else {
            agencyId = searchRequestVO.getAgencyId();

            currentIntIdsList = new ArrayList<>();
        }

        // Append excluded intersection ids from request
        String[] excludeIntIds = searchRequestVO.getExcludeIntIds();
        if (excludeIntIds != null && excludeIntIds.length > 0) {
            currentIntIdsList = Stream.concat(currentIntIdsList.stream(), Arrays.stream(excludeIntIds))
                    .distinct()
                    .toList();
        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .exclusionaryIds(currentIntIdsList)
                .shouldPaginate(searchRequestVO.getShouldPaginate())
                .page(searchRequestVO.getPage())
                .size(searchRequestVO.getSize())
                .status(searchRequestVO.getStatus())
                .text(searchRequestVO.getText())
                .build();
        IntersectionSearchResponseDataVO responseVO = intersectionStrategy.searchIntersectionsInternal(searchRequest);

        return IntersectionSearchResultObject.success(responseVO);
    }

    private Pair<List<ReportTemplate>, Long> getReportTemplatesAndCountFromDB(SummaryTemplateSearchRequestVO searchRequestVO) {
        OrderSpecifier<String>[] orderSpecifiers = ReportTemplateSearchHelper
                .createOrderBy(searchRequestVO.getOrderByColumns());

        ReportTemplateFilterData filterData = ReportTemplateFilterData.builder()
                .agencyId(searchRequestVO.getAgencyId())
                .createdAtFrom(searchRequestVO.getCreatedAtFrom())
                .createdAtTo(searchRequestVO.getCreatedAtTo())
                .ownerId(searchRequestVO.getOwnerId())
                .weekDays(searchRequestVO.getWeekDays())
                .aggregation(searchRequestVO.getAggregation())
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .status(searchRequestVO.getStatus())
                .text(searchRequestVO.getText())
                .build();

        List<ReportTemplate> templates = reportTemplateRepository.findPageByFilter(
                filterData,
                orderSpecifiers,
                searchRequestVO.getPage(),
                searchRequestVO.getSize());

        Long totalCount = reportTemplateRepository.countTotalByFilter(filterData);

        return Pair.of(templates, totalCount);
    }

    private void updateOwnerData(SimpleOwnerVO ownerVO) {
        if (ownerVO == null) {
            throw new IllegalArgumentException();
        }

        try {
            studioUserService.findById(Math.toIntExact(ownerVO.getId()))
                    .ifPresentOrElse(userDto -> {
                        ownerVO.setEmail(userDto.getEmail());
                        ownerVO.setName(NameUtil.usFullName(userDto.getFirstName(), userDto.getLastName()));
                    }, () -> {
                        ownerVO.setName(CommonConstants.UNKNOWN_USER_NAME);
                        ownerVO.setEmail(CommonConstants.UNKNOWN_USER_EMAIL);
                    });
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
        }
    }

    private void notifyOwnerAboutTemplateChange(SummaryTemplateDetailVO templateDetailVO,
                                                Long ownerId,
                                                TemplateConstant.ActionOnTemplate action) {
        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(ownerId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found owner's information of report template, template_id= {}", templateDetailVO.getId());
            return;
        }

        reportNotificationStrategy.sendSummaryTemplateNotiAsync(templateDetailVO, List.of(userOptional.get()), action);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void scanReportTemplateAsync() {
        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active report templates
        List<ReportTemplate> templates = reportTemplateRepository.findAllActiveReportTemplate();
        if (templates == null || templates.isEmpty()) {
            log.debug("No report template was triggered.");
            return;
        }

        // Find all the templates that can be processed
        List<Long> templateIds = templates.stream()
                .filter(template -> TemplateScheduleHelper
                        .isCandidateInTime(template.getSchedule(), template.getTimezoneId(), now, offset))
                .map(ReportTemplate::getId)
                .toList();

        if (!templateIds.isEmpty()) {
            log.debug("Found {} report template can be processed. Template ids: {}", templateIds.size(), templateIds);
            for (Long templateId : templateIds) {
                try {
                    // Process report template
                    Pair<ReportResult, Integer> reportResultPair = processReportTemplate(templateId);

                    // Send notification and email
                    notifyRunnerAboutReportResultCreated(reportResultPair.getFirst(), reportResultPair.getSecond());
                } catch (ProcessReportTemplateException e) {
                    log.error("Process report template: " + templateId + " failed!", e);
                } catch (Exception e) {
                    log.error("Exception occur while process template, templateId=" + templateId, e);
                }
            }
        }
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @Transactional
    public void runReportTemplateAsync(Long userId,
                                       Long templateId,
                                       Long jobId,
                                       List<IntersectionInternalVO> intersections) {
        Pair<ReportResult, Integer> reportResultPair;
        try {
            // Process report template
            reportResultPair = processReportTemplate(templateId, intersections);
            ReportResult reportResult = reportResultPair.getFirst();

            // Update report result's owner
            reportResult.setOwnerId(userId);
            reportResultRepository.saveAndFlush(reportResult);
        } catch (ProcessReportTemplateException e) {
            taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.ERROR, null);
            return;
        } catch (Exception e) {
            log.error("Exception occur while process template, templateId=" + templateId, e);
            taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.ERROR, null);
            return;
        }

        // Completed job progress
        taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.COMPLETED,
                createReportResultActionVO(reportResultPair.getFirst()));

        // Send notification and email
        notifyRunnerAboutReportResultCreated(reportResultPair.getFirst(), intersections.size());
    }

    @Override
    public SummaryMetricResultObject getAllMetrics() {
        SummaryMetricResponseVO metricResponseVO = new SummaryMetricResponseVO(
                ReportMetricBuilder.defaultAllMetricVOs());
        return new SummaryMetricResultObject(metricResponseVO, SimpleResultObject.SimpleStatusCode.SUCCESS);
    }

    /**
     * Returns report results and corresponding number of intersections
     */
    private Pair<ReportResult, Integer> processReportTemplate(Long templateId)
            throws ReportTemplateNotFoundException, ProcessReportTemplateException {
        IntersectionInternalListVO intersectionListVO = findAllIntersectionsInReportTemplate(templateId);
        if (intersectionListVO == null || intersectionListVO.getIntersections() == null
                || intersectionListVO.getIntersections().isEmpty()) {
            log.warn("Report template(id={}) doesn't have any available intersections", templateId);
            throw new ProcessReportTemplateException();
        }
        List<IntersectionInternalVO> intersections = intersectionListVO.getIntersections();

        return processReportTemplate(templateId, intersections);
    }

    private Pair<ReportResult, Integer> processReportTemplate(Long templateId,
                                                              List<IntersectionInternalVO> intersections)
            throws ProcessReportTemplateException {
        // 1. Find report template by id
        Optional<ReportTemplate> reportTemplateOpt = reportTemplateRepository.findById(templateId);
        if (reportTemplateOpt.isEmpty()) {
            log.error("Report template: " + templateId + " doesn't not found");
            throw new ProcessReportTemplateException();
        }
        ReportTemplate reportTemplate = reportTemplateOpt.get();

        // Resolve timezone for report processing with proper fallback handling
        String timeZone = timezoneFactory.validateAndResolveTimezone(
                reportTemplateOpt.get().getTimezoneId(),
                reportTemplateOpt.get().getAgencyId()
        );

        // 2. Create new report result
        ReportResult reportResult = null;
        try {
            reportResult = ReportResultBuilder.buildFromReportTemplate(reportTemplate, timeZone);
            // Reset ownerId for the report result because this result created be system
            // trigger automatically
            reportResult.setOwnerId(null);
            reportResult.setMetricJson(reportTemplate.getSchedule().getMetricJson());
        } catch (JsonProcessingException e) {
            log.error("Can't build report result from report template", e);
        } catch (Exception e) {
            log.error("Can't create report result from template", e);
        }

        if (reportResult == null) {
            log.error("Can't create report result from report template");
            throw new ProcessReportTemplateException();
        }

        reportResult = reportResultRepository.saveAndFlush(reportResult);

        // 4. Create new report result statistics with each intersection
        for (IntersectionInternalVO intersection : intersections) {
            // 4.1. Get phase statistics by date range
            List<PhaseStat> phaseStatList = getPhaseStatForReportResult(reportResult, intersection.getId());

            // 4.2. Aggregate data from phase statistics
            ReportResultStatistic reportResultStatistic = aggregateFromPhaseStat(phaseStatList,
                    reportResult.getAggregation(),
                    reportResult.getTimezone(), intersection);
            if (reportResultStatistic != null) {
                reportResultStatistic.setReportResult(reportResult);
                reportResultStatisticRepository.saveAndFlush(reportResultStatistic);
            }
        }

        return Pair.of(reportResult, intersections.size());
    }

    private void notifyRunnerAboutReportResultCreated(ReportResult reportResult, Integer numberOfIntersections) {
        Long userId = reportResult.getOwnerId();
        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(userId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found runner's information of report result!");
            return;
        }

        SummaryReportInfoVO summaryReportInfoVO = ReportResultBuilder.buildInfoVOFromReportResult(reportResult);

        // Update owner
        SimpleOwnerVO ownerVO = new SimpleOwnerVO(userId);
        updateOwnerData(ownerVO);
        summaryReportInfoVO.setOwnerVO(ownerVO);

        // Update agency name
        try {
            studioAgencyService.getAgencyById(reportResult.getAgencyId())
                    .map(StudioAgencyDto::getAgencyName)
                    .ifPresentOrElse(summaryReportInfoVO::setAgencyName,
                            () -> summaryReportInfoVO.setAgencyName(CommonConstants.NOT_AVAILABLE));
        } catch (StudioException e) {
            log.error("Error when get agency by id from Studio", e);
        }

        summaryReportInfoVO.setNumberOfIntersections(numberOfIntersections);

        reportNotificationStrategy.sendSummaryResultNotiAsync(summaryReportInfoVO, List.of(userOptional.get()),
                createReportResultActionVO(reportResult));
    }

    private ActionVO createReportResultActionVO(ReportResult reportResult) {
        ReportResultActionDataVO reportResultActionDataVO = ReportResultActionDataVO.builder()
                .agencyId(reportResult.getAgencyId())
                .reportResultId(reportResult.getId())
                .build();

        return ActionVO.builder()
                .data(reportResultActionDataVO)
                .target(ActionTarget.REPORT_RESULTS)
                .type(ActionType.REDIRECT)
                .btnTitle(TextKey.VIEW_BTN_TITLE)
                .build();
    }

    /**
     * Retrieve phase statistics from a report template of an intersection
     *
     * @param intUUID
     * @param reportResult
     * @return
     */
    private List<PhaseStat> getPhaseStatForReportResult(ReportResult reportResult, String intUUID) {
        // Get phase statistics by date range
        List<String> intUUIDList = Collections.singletonList(intUUID);

        // Find configurations that:
        // int_config.from_time_utc < toTime AND
        // int_config.to_time_utc >= fromTime
        List<IntersectionConfig> intConfigList = intConfigRepo
                .findAllByIntUUIDInAndToTimeUtcGreaterThanEqualAndFromTimeUtcLessThanOrderByFromTimeUtcAsc(
                        intUUIDList,
                        reportResult.getFromTimeUTC(),
                        reportResult.getToTimeUTC());

        List<Integer> coordinatedAndUnspecifiedPhases = new ArrayList<>(intConfigList.stream()
                .map(IntersectionConfig::getCoordinatedPhases)
                .flatMap(Collection::stream)
                .toList());
        coordinatedAndUnspecifiedPhases.add(Phase.UNSPECIFIED_PHASE_NUM);

        List<PhaseStat> allPhaseStatList = doGetPhaseStatFromDB(reportResult, intUUID, coordinatedAndUnspecifiedPhases);
        allPhaseStatList = summaryStatisticStrategy.fillMissingPhaseStats(reportResult.getFromTimeUTC(),
                allPhaseStatList);
        // Filter phase statistics base on time range
        List<PhaseStat> timeRangeFilteredPhaseStatList = new ArrayList<>();
        LocalTime startTime = reportResult.getFromTime().toLocalTime();
        LocalTime endTime = reportResult.getToTime().toLocalTime();
        String timeZone = reportResult.getTimezone();

        for (PhaseStat phaseStat : allPhaseStatList) {
            Timestamp fromTimeUTC = phaseStat.getFromTimeUTC();
            Timestamp toTimeUTC = phaseStat.getToTimeUTC();
            LocalTime localFromTime = fromTimeUTC.toInstant().atZone(ZoneId.of(timeZone)).toLocalTime();
            LocalTime localToTime = toTimeUTC.toInstant().atZone(ZoneId.of(timeZone)).toLocalTime();

            // Eliminate invalid time value
            // In case report template end time = 0, it means 24h. So, we don't care local
            // to time of phase statistics
            if (localFromTime.isBefore(startTime)
                    || (endTime.getHour() != 0 && localToTime.isAfter(endTime))
                    || (endTime.getHour() != 0 && localToTime.getHour() == 0)) {
                continue;
            }

            timeRangeFilteredPhaseStatList.add(phaseStat);
        }

        // Filter phase statistics base on day of weeks
        List<PhaseStat> weekDaysFilteredPhaseStatList = new ArrayList<>();
        Set<DayOfWeek> weekDays;
        try {
            ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

            weekDays = objectMapper.readValue(reportResult.getWeekDays(),
                    objectMapper.getTypeFactory().constructCollectionType(Set.class, DayOfWeek.class));
        } catch (JsonProcessingException e) {
            log.error("Error when parse week days string to object", e);
            return weekDaysFilteredPhaseStatList;
        }

        for (PhaseStat phaseStat : timeRangeFilteredPhaseStatList) {
            Timestamp fromTimeUTC = phaseStat.getFromTimeUTC();
            LocalDate localDate = fromTimeUTC.toInstant().atZone(ZoneId.of(timeZone)).toLocalDate();
            DayOfWeek dayOfWeek = localDate.getDayOfWeek();
            if (weekDays.contains(dayOfWeek)) {
                weekDaysFilteredPhaseStatList.add(phaseStat);
            }
        }

        return weekDaysFilteredPhaseStatList;
    }

    private List<PhaseStat> doGetPhaseStatFromDB(ReportResult reportResult, String intUUID, List<Integer> phases) {
        PhaseStatFilterDataVO filterDataVO = PhaseStatFilterDataVO.builder()
                .intUUIDList(Collections.singletonList(intUUID))
                .fromTimeUTC(reportResult.getFromTimeUTC())
                .toTimeUTC(reportResult.getToTimeUTC())
                .phases(phases)
                .build();

        return phaseStatRepository.findAllByFilter(filterDataVO);
    }

    public IntersectionInternalListVO findAllIntersectionsInReportTemplate(Long templateId)
            throws ReportTemplateNotFoundException {
        Optional<ReportTemplate> templateOptional = reportTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            throw new ReportTemplateNotFoundException();
        }

        ReportTemplate reportTemplate = templateOptional.get();
        String intersectionListStr = reportTemplate.getIntersectionList();
        IntersectionInternalListVO intersectionListVO;

        if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionListStr)) {
            IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                    .agencyId(reportTemplate.getAgencyId())
                    .status(IntersectionStatus.AVAILABLE.getInsight()) //Exclude UNAVAILABLE intersection
                    .shouldPaginate(false)
                    .build();
            intersectionListVO = intersectionStrategy.getIntersectionsByFilterInternal(searchRequest);
        } else {
            List<String> currentIntersectionIdsList = Arrays.asList(reportTemplate.getIntersectionList()
                    .split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
            IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                    .agencyId(reportTemplate.getAgencyId())
                    .intersectionIds(currentIntersectionIdsList)
                    .status(IntersectionStatus.AVAILABLE.getInsight()) //Exclude UNAVAILABLE intersection
                    .shouldPaginate(false)
                    .build();
            intersectionListVO = intersectionStrategy.getIntersectionsByFilterInternal(searchRequest);
        }

        return intersectionListVO;
    }

    private ReportResultStatistic aggregateFromPhaseStat(List<PhaseStat> phaseStatList,
                                                         TemplateAggregation aggregation,
                                                         String timezone,
                                                         IntersectionInternalVO intersection) {
        List<StatisticBlock> unitBlockList = summaryStatisticStrategy
                .aggregateStatisticBlock(phaseStatList, aggregation, timezone);

        ReportResultStatistic reportResultStatistic = null;
        try {
            reportResultStatistic = ReportResultBuilder.buildResultStatisticFromUnitBlock(intersection, unitBlockList);
        } catch (JsonProcessingException e) {
            log.error("Error parse JSON data", e);
        }

        return reportResultStatistic;
    }

    @Override
    @Transactional
    @AgencyAware(agencyId = "[0]")
    public void scanAgencyReportTemplateAsync(int agencyId) {
        log.info("Scan scheduled summary reports for agency: {}", agencyId);
        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active report templates
        List<ReportTemplate> templates = reportTemplateRepository.findAllActiveReportTemplate();
        if (templates == null || templates.isEmpty()) {
            log.debug("No report template was triggered.");
            return;
        }

        // Find all the templates that can be processed
        List<Long> templateIds = templates.stream()
                .filter(template -> TemplateScheduleHelper
                        .isCandidateInTime(template.getSchedule(), template.getTimezoneId(), now, offset))
                .map(ReportTemplate::getId)
                .toList();

        log.info("Found {} summary templates for agency: {}", templateIds.size(), agencyId);
        if (!templateIds.isEmpty()) {
            log.debug("Found {} report template can be processed. Template ids: {}", templateIds.size(), templateIds);
            for (Long templateId : templateIds) {
                try {
                    // Process report template
                    Pair<ReportResult, Integer> reportResultPair = processReportTemplate(templateId);

                    // Send notification and email
                    notifyRunnerAboutReportResultCreated(reportResultPair.getFirst(), reportResultPair.getSecond());
                } catch (ProcessReportTemplateException e) {
                    log.error("Process report template: " + templateId + " failed!", e);
                } catch (Exception e) {
                    log.error("Exception occur while process template, templateId=" + templateId, e);
                }
            }
        }
    }

}
