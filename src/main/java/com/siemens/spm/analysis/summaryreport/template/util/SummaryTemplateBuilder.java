package com.siemens.spm.analysis.summaryreport.template.util;

import java.time.DayOfWeek;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.summaryreport.SummaryTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateDetailVO;
import com.siemens.spm.analysis.domain.ReportTemplate;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.extern.slf4j.Slf4j;

/**
 * Helper class for build report template entity and VO object
 *
 * <AUTHOR> Nguyen
 */
@Slf4j
public final class SummaryTemplateBuilder {

    private SummaryTemplateBuilder() {
    }

    public static SummaryTemplateDetailVO buildTemplateDetailVO(ReportTemplate template) {
        SummaryTemplateDetailVO templateDetailVO = buildCoreDataFromEntity(template);

        // Update schedule data
        TemplateSchedule schedule = template.getSchedule();
        if (schedule != null) {
            templateDetailVO.setScheduleVO(TemplateScheduleBuilder.buildVOFromEntity(schedule));
        }

        // Update intersections data
        String intersectionIdsString = template.getIntersectionList();
        IntersectionIdsVO intersectionIdsVO;
        if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionIdsString)) {
            intersectionIdsVO = IntersectionIdsVO.resolveAllIntersection();
        } else {
            intersectionIdsVO = IntersectionIdsVO.resolveSpecificIntersectionFromString(intersectionIdsString);
        }
        templateDetailVO.setIntersectionIdsVO(intersectionIdsVO);
        templateDetailVO.setTimezoneId(template.getTimezoneId());
        return templateDetailVO;
    }

    public static ReportTemplate buildEntityFromCreateRequestVO(SummaryTemplateCreateRequestVO requestVO) {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

        String dateRange;
        String weekDays;
        try {
            dateRange = objectMapper.writeValueAsString(DateRange.resolveDateRange(requestVO.getDateRange()));
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Failed to parse the date range!");
        }

        try {
            weekDays = objectMapper.writeValueAsString(requestVO.getWeekDays());
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Failed to parse the week days!");
        }

        return ReportTemplate.builder()
                .name(requestVO.getName())
                .description(requestVO.getDescription())
                .agencyId(requestVO.getAgencyId())
                .startTime(requestVO.getStartTime())
                .endTime(requestVO.getEndTime())
                .timezone(requestVO.getTimeZone())
                .timezoneId(requestVO.getTimezoneId())
                .aggregation(requestVO.getAggregation())
                .dateRange(dateRange)
                .weekDays(weekDays)
                .intersectionList(IntersectionIdsVO.resolveIntersectionIdsString(requestVO.getIntersectionIdsVO()))
                .status(TemplateStatus.ACTIVE)
                .deleted(false)
                .build();
    }

    public static SummaryTemplateDetailVO buildCoreDataFromEntity(ReportTemplate template) {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

        DateRange dateRange;
        try {
            dateRange = objectMapper.readValue(template.getDateRange(), DateRange.class);
        } catch (JsonProcessingException e) {
            log.error("Error when parse dateRange string to object", e);
            throw new IllegalArgumentException("Date range is invalid!");
        }

        Set<DayOfWeek> weekDays;
        try {
            weekDays = objectMapper.readValue(template.getWeekDays(),
                    objectMapper.getTypeFactory().constructCollectionType(Set.class, DayOfWeek.class));
        } catch (JsonProcessingException e) {
            log.error("Error when parse week days string to object", e);
            throw new IllegalArgumentException("Week days is invalid!");
        }

        return SummaryTemplateDetailVO.builder()
                .id(template.getId())
                .name(template.getName())
                .description(template.getDescription())
                .agencyId(template.getAgencyId())
                .ownerId(template.getOwnerId())
                .aggregation(template.getAggregation())
                .dateRange(dateRange)
                .weekDays(new TreeSet<>(weekDays))
                .ownerVO(new SimpleOwnerVO(template.getOwnerId()))
                .status(template.getStatus())
                .startTime(template.getStartTime())
                .endTime(template.getEndTime())
                .timeZone(template.getTimezone())
                .timezoneId(template.getTimezoneId())
                .createdAt(template.getCreatedAt())
                .build();
    }

    /**
     * Update request to report template entity
     *
     * @param template                entity need to update
     * @param updateCoreDataRequestVO {@code ReportTemplateUpdateCoreDataRequestVO} update request
     */
    public static void updateVOToEntity(ReportTemplate template,
                                        SummaryTemplateUpdateCoreDataRequestVO updateCoreDataRequestVO) {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();
        String dateRange;
        String weekDays;
        try {
            dateRange = objectMapper.writeValueAsString(updateCoreDataRequestVO.getDateRange());
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Date range is invalid!");
        }

        try {
            weekDays = objectMapper.writeValueAsString(updateCoreDataRequestVO.getWeekDays());
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException("Week days is invalid!");
        }

        template.setName(updateCoreDataRequestVO.getName());
        template.setDescription(updateCoreDataRequestVO.getDescription());
        template.setOwnerId(updateCoreDataRequestVO.getOwnerId());
        template.setAgencyId(updateCoreDataRequestVO.getAgencyId());
        template.setWeekDays(weekDays);
        template.setTimezone(updateCoreDataRequestVO.getTimeZone());
        template.setTimezoneId(updateCoreDataRequestVO.getTimezoneId());
        template.setStartTime(updateCoreDataRequestVO.getStartTime());
        template.setEndTime(updateCoreDataRequestVO.getEndTime());
        template.setAggregation(updateCoreDataRequestVO.getAggregation());
        template.setDateRange(dateRange);
    }

}
