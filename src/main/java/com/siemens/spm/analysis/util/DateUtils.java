package com.siemens.spm.analysis.util;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Admin
 * @version : 1.0
 * @since : 30/6/2025
 **/
public final class DateUtils {

    private DateUtils() {
    }

    /**
     * Get number of days between from date and to date
     *
     * @param from from date
     * @param to   to date
     * @return number of days
     */
    public static long getNumberOfDays(LocalDateTime from, LocalDateTime to) {
        return Duration.between(from, to).toDays();
    }

    /**
     * Get number of days between from date and to date
     *
     * @param targetDateTimes list of target date times
     * @return number of days
     */
    public static long getNumberOfDays(List<LocalDateTime> targetDateTimes) {
        Set<LocalDate> groupedByDate = targetDateTimes.stream()
                .map(LocalDateTime::toLocalDate)
                .collect(Collectors.toSet());
        return groupedByDate.size();
    }

    /**
     * Converts a LocalDateTime from one timezone to another.
     *
     * @param dateTime   the local date time to convert
     * @param sourceZone the source timezone ID (e.g., "UTC", "America/New_York")
     * @param targetZone the target timezone ID (e.g., "UTC", "Europe/London")
     * @return the converted LocalDateTime in the target timezone
     * @throws java.time.DateTimeException if the zone ID is invalid
     */
    public static LocalDateTime convertTimezone(LocalDateTime dateTime, String sourceZone, String targetZone) {
        if (dateTime == null || sourceZone == null || targetZone == null) {
            throw new IllegalArgumentException("Parameters cannot be null");
        }

        if (sourceZone.equals(targetZone)) {
            return dateTime;
        }

        return dateTime.atZone(ZoneId.of(sourceZone))
                .withZoneSameInstant(ZoneId.of(targetZone))
                .toLocalDateTime();
    }

}
