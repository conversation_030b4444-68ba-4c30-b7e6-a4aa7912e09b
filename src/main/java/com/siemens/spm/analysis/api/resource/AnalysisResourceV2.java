/*
 * Copyright (C) Siemens.  All Rights Reserved.
 *
 * Source      : AnalysisResource.java
 * Project     : SPM Platform
 */

package com.siemens.spm.analysis.api.resource;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import com.siemens.spm.analysis.api.boundary.AnalysisService;
import com.siemens.spm.analysis.api.controller.AnalysisControllerV2;
import com.siemens.spm.analysis.api.vo.enums.BinSize;
import com.siemens.spm.analysis.api.vo.response.AnalysisMetadataResultObject;
import com.siemens.spm.analysis.api.vo.response.AnalysisResultNonChartObject;
import com.siemens.spm.analysis.api.vo.response.AnalysisResultObject;
import com.siemens.spm.analysis.api.vo.response.MetricDayListResultObject;
import com.siemens.spm.analysis.api.vo.response.PhaseFilterResultObject;
import com.siemens.spm.analysis.exception.AnalysisInitializationException;
import com.siemens.spm.analysis.exception.InvalidPerfLogException;
import com.siemens.spm.analysis.factory.AogAnalysisFactory;
import com.siemens.spm.analysis.factory.AorAnalysisFactory;
import com.siemens.spm.analysis.factory.AppDelayAnalysisFactory;
import com.siemens.spm.analysis.factory.CoordHealthAnalysisFactory;
import com.siemens.spm.analysis.factory.SplitFailureAnalysisFactory;
import com.siemens.spm.analysis.factory.pp.PpAnalysisFactory;
import com.siemens.spm.analysis.vo.AogAnalysisVO;
import com.siemens.spm.analysis.vo.AogChartVO;
import com.siemens.spm.analysis.vo.AorAnalysisVO;
import com.siemens.spm.analysis.vo.AorChartVO;
import com.siemens.spm.analysis.vo.AppDelayAnalysisVO;
import com.siemens.spm.analysis.vo.AppDelayChartVO;
import com.siemens.spm.analysis.vo.CoordinationChartVO;
import com.siemens.spm.analysis.vo.CoordinationHealthAnalysisVO;
import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
import com.siemens.spm.analysis.vo.PedChartVO;
import com.siemens.spm.analysis.vo.PtChartVO;
import com.siemens.spm.analysis.vo.SplitFailureAnalysisVO;
import com.siemens.spm.analysis.vo.SplitFailureChartVO;
import com.siemens.spm.analysis.vo.VolumeBinVO;
import com.siemens.spm.analysis.vo.VolumeChartVO;
import com.siemens.spm.analysis.vo.YellowTrapAnalysisVO;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataChartVO;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisChartVO;
import com.siemens.spm.analysis.vo.pp.PpAnalysisVO;
import com.siemens.spm.analysis.vo.pp.PpChartVO;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthChartVO;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationChartVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.analysis.vo.turningmovement.TurningMovementChartVO;
import com.siemens.spm.common.util.ResponseUtil;
import com.siemens.spm.perflog.vo.PerfLogBundleVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
public class AnalysisResourceV2 implements AnalysisControllerV2 {

    @Autowired
    private AnalysisService analysisService;

    private static final String INTERSECTION_NOT_AVAILABLE_MSG = "Intersection(id={}) is not available in agency(id={})";
    private static final String ANALYSIS_DEBUG_MSG = "METHOD=%s | PERFLOG_INTERVAL=%d | RUNTIME=%d.%09d";
    private static final String INVALID_PERFLOG_DATA_MSG = "Invalid perflog data";
    private static final String INITIALIZE_ANALYSIS_ERROR_MSG = "Unable to initialize analysis";

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<AorChartVO>> getAorAnalysis(Integer agencyId,
                                                                           String intUUID,
                                                                           LocalDateTime fromTime,
                                                                           LocalDateTime toTime,
                                                                           Integer binSize) {
        AnalysisResultObject<AorChartVO> resultObject;

        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getAorAnalysis(agencyId, intUUID, fromTime, toTime,
                    binSize != null ? binSize : VolumeBinVO.DEFAULT_BIN_SIZE);
        } catch (Exception e) {
            log.error("Failed to get AOR analysis", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getAorAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<AnalysisResultObject<AorChartVO>> testGetAorAnalysis(Integer agencyId, String intUUID,
                                                                               LocalDateTime fromTime,
                                                                               LocalDateTime toTime, Integer binSize,
                                                                               PerfLogBundleVO perfLogBundleVO) {
        // Create Analysis from responded PerfLogBundleVO
        AorAnalysisVO analysisVO;
        AorAnalysisFactory analysisFactory = new AorAnalysisFactory(binSize);
        AnalysisResultObject<AorChartVO> resultObject;
        try {
            analysisVO = analysisFactory.createAnalysis(fromTime, toTime, perfLogBundleVO, Collections.emptyList());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (AnalysisInitializationException e) {
            log.error("Invalid analysis initialization", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseEntity.ok(resultObject);
    }

    @Override
//    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<AogChartVO>> getAogAnalysis(Integer agencyId,
                                                                           String intUUID,
                                                                           LocalDateTime fromTime,
                                                                           LocalDateTime toTime,
                                                                           Integer binSize) {
        AnalysisResultObject<AogChartVO> resultObject;

        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getAogAnalysis(agencyId, intUUID, fromTime, toTime,
                    binSize != null ? binSize : VolumeBinVO.DEFAULT_BIN_SIZE);
        } catch (Exception e) {
            log.error("Failed to get AOG analysis", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getAogAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * Only use for testing
     */
    @Override
    public ResponseEntity<AnalysisResultObject<AogChartVO>> testGetAogAnalysis(Integer agencyId, String intUUID,
                                                                               LocalDateTime fromTime,
                                                                               LocalDateTime toTime, Integer binSize,
                                                                               PerfLogBundleVO perfLogBundleVO) {
        // Create Analysis from responded PerfLogBundleVO
        AogAnalysisVO analysisVO;
        AogAnalysisFactory analysisFactory = new AogAnalysisFactory(binSize);
        AnalysisResultObject<AogChartVO> resultObject;
        try {
            analysisVO = analysisFactory.createAnalysis(fromTime, toTime, perfLogBundleVO, Collections.emptyList());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (AnalysisInitializationException e) {
            log.error("Invalid analysis initialization", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseEntity.ok(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<CoordinationChartVO>> getCoordinationAnalysis(Integer agencyId,
                                                                                             String intUUID,
                                                                                             LocalDateTime fromTime,
                                                                                             LocalDateTime toTime,
                                                                                             Integer binSize) {
        AnalysisResultObject<CoordinationChartVO> resultObject;

        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getCoordinationAnalysis(agencyId, intUUID, fromTime, toTime,
                    binSize != null ? binSize : VolumeBinVO.DEFAULT_BIN_SIZE);
        } catch (Exception e) {
            log.error("Failed to get Coordination analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getCoordinationAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<PtChartVO>> getPtAnalysis(Integer agencyId,
                                                                         String intUUID,
                                                                         LocalDateTime fromTime,
                                                                         LocalDateTime toTime) {
        AnalysisResultObject<PtChartVO> resultObject;

        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getPtAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get PT analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getPtAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics', 'report') " +
            "&& hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisMetadataResultObject> getAllAnalysisMetadata(Integer agencyId) {
        AnalysisMetadataResultObject resultObject;

        try {
            resultObject = analysisService.getAllAnalysisMetadata();
        } catch (Exception e) {
            log.error("Failed to get all analysis metadata", e);

            resultObject = new AnalysisMetadataResultObject(AnalysisMetadataResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<MetricDayListResultObject> getAnalysisDays(Integer agencyId,
                                                                     String analysisID,
                                                                     String intUUID,
                                                                     LocalDate fromDate,
                                                                     LocalDate toDate) {
        MetricDayListResultObject resultObject;
        try {
            resultObject = analysisService.getAnalysisDays(analysisID, intUUID, fromDate, toDate);
        } catch (Exception e) {
            log.error("Failed to get analysis days", e);

            resultObject = new MetricDayListResultObject(MetricDayListResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<PhaseFilterResultObject> getPhaseFilter(Integer agencyId,
                                                                  String analysisType,
                                                                  String intUUID,
                                                                  LocalDateTime fromTime,
                                                                  LocalDateTime toTime) {

        PhaseFilterResultObject resultObject;
        try {
            resultObject = analysisService.getPhaseFilter(analysisType, intUUID, fromTime,
                    toTime);
        } catch (Exception e) {
            log.error("Failed to get phase filter", e);

            resultObject = new PhaseFilterResultObject(PhaseFilterResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<CoordinationHealthChartVO>> getCoordinationHealthAnalysis(
            Integer agencyId,
            String intUUID,
            LocalDateTime fromTime,
            LocalDateTime toTime,
            Integer binSize) {
        AnalysisResultObject<CoordinationHealthChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }
        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getCoordinationHealthAnalysis(agencyId, intUUID, fromTime, toTime,
                    binSize != null ? binSize : VolumeBinVO.DEFAULT_BIN_SIZE);
        } catch (Exception e) {
            log.error("Failed to get coordination health analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }
        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);

        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getCoordinationHealthAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);

    }

    /**
     * Only use for testing
     */
    @Override
    public ResponseEntity<AnalysisResultObject<CoordinationHealthChartVO>> testGetCoordHealthAnalysis(Integer agencyId,
                                                                                                      String intUUID,
                                                                                                      LocalDateTime fromTime,
                                                                                                      LocalDateTime toTime,
                                                                                                      Integer binSize,
                                                                                                      PerfLogBundleVO perfLogBundleVO) {
        CoordinationHealthAnalysisVO analysisVO;
        CoordHealthAnalysisFactory factory = new CoordHealthAnalysisFactory(binSize);
        AnalysisResultObject<CoordinationHealthChartVO> resultObject;
        try {
            analysisVO = factory.createAnalysis(fromTime, toTime, perfLogBundleVO, Collections.emptyList());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (IllegalArgumentException | SecurityException | AnalysisInitializationException e) {
            log.error(INITIALIZE_ANALYSIS_ERROR_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseEntity.ok(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<SplitFailureChartVO>> getSplitFailureAnalysis(Integer agencyId,
                                                                                             String intUUID,
                                                                                             LocalDateTime fromTime,
                                                                                             LocalDateTime toTime) {
        AnalysisResultObject<SplitFailureChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getSplitFailureAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get split failure analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }
        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getSplitFailureAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * Only use for testing
     */
    @Override
    public ResponseEntity<AnalysisResultObject<SplitFailureChartVO>> testSplitFailureAnalysis(Integer agencyId,
                                                                                              String intUUID,
                                                                                              LocalDateTime fromTime,
                                                                                              LocalDateTime toTime,
                                                                                              PerfLogBundleVO perfLogBundleVO) {
        SplitFailureAnalysisVO analysisVO;
        SplitFailureAnalysisFactory factory = new SplitFailureAnalysisFactory();
        AnalysisResultObject<SplitFailureChartVO> resultObject;
        try {
            analysisVO = factory.createAnalysis(fromTime, toTime, perfLogBundleVO, Collections.emptyList());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (IllegalArgumentException | SecurityException | AnalysisInitializationException e) {
            log.error(INITIALIZE_ANALYSIS_ERROR_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseEntity.ok(resultObject);
    }

    /**
     * Only use for testing
     */
    @Override
    public ResponseEntity<AnalysisResultObject<AppDelayChartVO>> testAppDelayAnalysis(Integer agencyId,
                                                                                      String intUUID,
                                                                                      LocalDateTime fromTime,
                                                                                      LocalDateTime toTime,
                                                                                      Integer binSize,
                                                                                      PerfLogBundleVO perfLogBundleVO) {
        AppDelayAnalysisVO analysisVO;
        AppDelayAnalysisFactory factory = new AppDelayAnalysisFactory(binSize);
        AnalysisResultObject<AppDelayChartVO> resultObject;
        try {
            analysisVO = factory.createAnalysis(fromTime, toTime, perfLogBundleVO, Collections.emptyList());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (IllegalArgumentException | SecurityException | AnalysisInitializationException e) {
            log.error(INITIALIZE_ANALYSIS_ERROR_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseEntity.ok(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<QueueLengthChartVO>> getQueueLengthAnalysis(Integer agencyId,
                                                                                           String intUUID,
                                                                                           LocalDateTime fromTime,
                                                                                           LocalDateTime toTime,
                                                                                           Integer binSize) {
        AnalysisResultObject<QueueLengthChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            if (binSize == null) {
                binSize = VolumeBinVO.DEFAULT_BIN_SIZE;
            }
            resultObject = analysisService.getQueueLengthAnalysis(agencyId, intUUID, fromTime, toTime, binSize);
        } catch (Exception e) {
            log.error("Failed to get queue length analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getQueueLengthAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<VolumeChartVO>> getVolumeAnalysis(Integer agencyId,
                                                                                 String intUUID,
                                                                                 LocalDateTime fromTime,
                                                                                 LocalDateTime toTime,
                                                                                 Integer binSize) {
        AnalysisResultObject<VolumeChartVO> resultObject;

        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getVolumeAnalysis(agencyId, intUUID, fromTime, toTime,
                    binSize != null ? binSize : VolumeBinVO.DEFAULT_BIN_SIZE);
        } catch (Exception e) {
            log.error("Failed to get volume analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getVolumeAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);

    }

    @Override
    public ResponseEntity<AnalysisResultNonChartObject<YellowTrapAnalysisVO>> testGetYellowTrapAnalysis(Integer agencyId,
                                                                                                        String intUUID,
                                                                                                        LocalDateTime fromTime,
                                                                                                        LocalDateTime toTime,
                                                                                                        PerfLogBundleVO perfLogBundleVO) {
        AnalysisResultNonChartObject<YellowTrapAnalysisVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultNonChartObject<>(
                    AnalysisResultNonChartObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getYellowChartAnalysis(agencyId, intUUID, fromTime, toTime,
                    perfLogBundleVO);
        } catch (Exception e) {
            log.error("Failed to get yellow trap analysis", e);

            resultObject = new AnalysisResultNonChartObject<>(AnalysisResultNonChartObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.testGetYellowTrapAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<SplitMonitorChartVO>> getSplitMonitorAnalysis(Integer agencyId,
                                                                                             String intUUID,
                                                                                             LocalDateTime fromTime,
                                                                                             LocalDateTime toTime) {
        AnalysisResultObject<SplitMonitorChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getSplitMonitorAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get split monitor analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getSplitMonitorAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<AnalysisResultObject<SplitMonitorChartVO>> testGetSplitMonitorAnalysis(Integer agencyId,
                                                                                                 String intUUID,
                                                                                                 LocalDateTime fromTime,
                                                                                                 LocalDateTime toTime,
                                                                                                 PerfLogBundleVO perfLogBundleVO) {
        AnalysisResultObject<SplitMonitorChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(
                    AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.testGetSplitMonitorAnalysis(agencyId, intUUID, fromTime, toTime,
                    perfLogBundleVO);
        } catch (Exception e) {
            log.error("Failed to get split monitor analysis", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.testGetSplitMonitorAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<PpChartVO>> getPpAnalysis(Integer agencyId,
                                                                         String intUUID,
                                                                         LocalDateTime fromTime,
                                                                         LocalDateTime toTime) {
        AnalysisResultObject<PpChartVO> resultObject;

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getPpAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get pp analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getPpAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<AnalysisResultObject<PpChartVO>> testGetPpAnalysis(Integer agencyId,
                                                                             String intUUID,
                                                                             LocalDateTime fromTime,
                                                                             LocalDateTime toTime,
                                                                             PerfLogBundleVO perfLogBundleVO) {
        AnalysisResultObject<PpChartVO> resultObject;
        try {
            PpAnalysisVO analysisVO = new PpAnalysisFactory()
                    .createAnalysis(fromTime, toTime, perfLogBundleVO, List.of());
            resultObject = new AnalysisResultObject<>(analysisVO);
        } catch (InvalidPerfLogException e) {
            log.error(INVALID_PERFLOG_DATA_MSG, e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INVALID_PERFLOG_DATA);
        } catch (Exception e) {
            log.error("Failed to get pp analysis", e);
            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<TurningMovementChartVO>> getTurningMovementAnalysis(Integer agencyId,
                                                                                                   String intUUID,
                                                                                                   LocalDateTime fromTime,
                                                                                                   LocalDateTime toTime,
                                                                                                   Integer binSize) {
        AnalysisResultObject<TurningMovementChartVO> resultObject;

        Instant before = Instant.now(); // APP_DEBUG

        try {
            binSize = binSize == null ? BinSize.FIVE_MINUTES.getValueInSecond() : binSize;
            resultObject = analysisService.getTurningMovementAnalysis(agencyId, intUUID, fromTime, toTime, binSize);
        } catch (Exception e) {
            log.error("Failed to get turning movement analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getTurningMovementAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<PedChartVO>> getPedestrianAnalysis(Integer agencyId,
                                                                                  String intUUID,
                                                                                  LocalDateTime fromTime,
                                                                                  LocalDateTime toTime) {
        AnalysisResultObject<PedChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getPedestrianAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get pedestrian analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getPedestrianAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<AppDelayChartVO>> getAppDelayAnalysis(Integer agencyId,
                                                                                     String intUUID,
                                                                                     LocalDateTime fromTime,
                                                                                     LocalDateTime toTime,
                                                                                     Integer binSize) {
        AnalysisResultObject<AppDelayChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getAppDelayAnalysis(agencyId, intUUID, fromTime, toTime, binSize);
        } catch (Exception e) {
            log.error("Failed to get app delay analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }
        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getAppDelayAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    /**
     * Abnormal data analysis function
     */
    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<AbnormalDataChartVO>> getAbnormalAnalysis(Integer agencyId,
                                                                                         String intUUID,
                                                                                         LocalDate targetDate,
                                                                                         Integer daysPeriod,
                                                                                         LocalTime fromTime,
                                                                                         LocalTime toTime) {
        AnalysisResultObject<AbnormalDataChartVO> resultObject;
        if (!analysisService.isIntersectionAvailable(agencyId, intUUID)) {
            log.error(INTERSECTION_NOT_AVAILABLE_MSG, intUUID, agencyId);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.INTERSECTION_NOT_AVAILABLE);
            return ResponseUtil.wrapOrNotFound(resultObject);
        }

        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getAbnormalDataAnalysis(agencyId, intUUID, targetDate, daysPeriod,
                    fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get abnormal data analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getAbnormalAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    public ResponseEntity<AnalysisResultObject<AbnormalDataChartVO>> testGetAbnormalAnalysis(Integer agencyId,
                                                                                             String intUUID,
                                                                                             LocalDate targetDate,
                                                                                             Integer daysPeriod,
                                                                                             LocalTime fromTime,
                                                                                             LocalTime toTime,
                                                                                             PerfLogBundleVO perfLogBundleVO) {
        AnalysisResultObject<AbnormalDataChartVO> resultObject;
        Instant before = Instant.now(); // APP_DEBUG
        try {
            resultObject = analysisService.getAbnormalDataAnalysis(agencyId, intUUID, targetDate, daysPeriod,
                    fromTime, toTime, perfLogBundleVO);
        } catch (Exception e) {
            log.error("Failed to get abnormal data analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.testGetAbnormalAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<RedLightViolationChartVO>> getRedLightViolationAnalysis(Integer agencyId,
                                                                                                       String intUUID,
                                                                                                       LocalDateTime fromTime,
                                                                                                       LocalDateTime toTime,
                                                                                                       Integer binSize) {
        AnalysisResultObject<RedLightViolationChartVO> resultObject;

        Instant before = Instant.now(); // APP_DEBUG

        try {
            binSize = binSize == null ? BinSize.FIVE_MINUTES.getValueInSecond() : binSize;
            resultObject = analysisService.getRedLightViolationAnalysis(agencyId, intUUID, fromTime, toTime, binSize);
        } catch (Exception e) {
            log.error("Failed to get red light violation analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getTurningMovementAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);
    }

    @Override
    @PreAuthorize("hasAnyLicenses(#agencyId, 'performance_metrics') && hasPermission('analysis', 'READ', #agencyId, null)")
    public ResponseEntity<AnalysisResultObject<MOEAnalysisChartVO>> getMOEAnalysis(Integer agencyId,
                                                                                   String intUUID,
                                                                                   LocalDateTime fromTime,
                                                                                   LocalDateTime toTime) {
        AnalysisResultObject<MOEAnalysisChartVO> resultObject;
        Instant before = Instant.now(); // APP_DEBUG

        try {
            resultObject = analysisService.getMOEAnalysis(agencyId, intUUID, fromTime, toTime);
        } catch (Exception e) {
            log.error("Failed to get MOE analysis", e);

            resultObject = new AnalysisResultObject<>(AnalysisResultObject.StatusCode.UNKNOWN_ERROR);
        }

        Instant after = Instant.now(); // APP_DEBUG
        Duration duration = Duration.between(before, after);
        String debugMessage = String.format(
                ANALYSIS_DEBUG_MSG,
                "analysisService.getMOEAnalysis",
                Duration.between(fromTime, toTime).getSeconds(),
                duration.getSeconds(), duration.getNano());
        log.debug(debugMessage);

        return ResponseUtil.wrapOrNotFound(resultObject);

    }
}
