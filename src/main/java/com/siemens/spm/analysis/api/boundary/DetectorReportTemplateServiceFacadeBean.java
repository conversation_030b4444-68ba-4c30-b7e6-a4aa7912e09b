package com.siemens.spm.analysis.api.boundary;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.UpdateRequestAction;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateProcessResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateSearchResultObject;
import com.siemens.spm.analysis.detectorreport.exception.DetectorTemplateIntersectionEmptyException;
import com.siemens.spm.analysis.detectorreport.exception.DetectorTemplateInvalidException;
import com.siemens.spm.analysis.detectorreport.exception.DetectorTemplateNotFoundException;
import com.siemens.spm.analysis.detectorreport.template.DetectorTemplateStrategy;
import com.siemens.spm.analysis.job.JobProgressStrategy;
import com.siemens.spm.common.agency.master.AgencySchemaReadService;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.perflog.persistence.TaskProgress;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class DetectorReportTemplateServiceFacadeBean implements DetectorReportTemplateService {

    private final DetectorTemplateStrategy detectorTemplateStrategy;
    private final JobProgressStrategy jobProgressStrategy;
    private final StudioUserService studioUserService;
    private final AgencySchemaReadService agencySchemaReadService;

    public DetectorReportTemplateServiceFacadeBean(DetectorTemplateStrategy detectorTemplateStrategy,
                                                   JobProgressStrategy jobProgressStrategy,
                                                   StudioUserService studioUserService,
                                                   AgencySchemaReadService agencySchemaReadService) {
        this.detectorTemplateStrategy = detectorTemplateStrategy;
        this.jobProgressStrategy = jobProgressStrategy;
        this.studioUserService = studioUserService;
        this.agencySchemaReadService = agencySchemaReadService;
    }

    @Override
    public DetectorTemplateDetailResultObject createReportTemplate(DetectorTemplateCreateRequestVO requestVO) {
        return detectorTemplateStrategy.createReportTemplate(requestVO);
    }

    @Override
    public DetectorTemplateCoreDataResultObject getTemplateCoreData(Long templateId) {
        return detectorTemplateStrategy.getTemplateCoreData(templateId);
    }

    @Override
    public DetectorMetricResultObject getAllMetrics() {
        return detectorTemplateStrategy.getAllMetrics();
    }

    @Override
    public DetectorScheduleResultObject getTemplateSchedule(Long templateId) {
        return detectorTemplateStrategy.getTemplateSchedule(templateId);
    }

    @Override
    public IntersectionSearchResultObject searchAvailableTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        return detectorTemplateStrategy.searchAvailableTemplateIntersections(searchRequestVO);
    }

    @Override
    public DetectorTemplateManipulateResultObject updateCoreData(Long templateId, DetectorTemplateUpdateCoreDataRequestVO updateRequestVO) {
        return detectorTemplateStrategy.updateCoreData(templateId, updateRequestVO);
    }

    @Override
    public DetectorTemplateManipulateResultObject updateTemplateScheduleData(Long templateId,
                                                                             DetectorTemplateScheduleVO templateScheduleVO) {
        return detectorTemplateStrategy.updateTemplateScheduleData(templateId, templateScheduleVO);
    }

    @Override
    public DetectorTemplateManipulateResultObject softDeletes(List<Long> templateIds) {
        return detectorTemplateStrategy.softDeletes(templateIds);
    }

    @Override
    public DetectorTemplateManipulateResultObject updateIntersections(Long templateId,
                                                                      IntersectionIdsRequestVO intersectionIdsRequestVO) {

        DetectorTemplateManipulateResultObject resultObject;
        UpdateRequestAction action = intersectionIdsRequestVO.getAction();
        switch (action) {
        case ADD -> resultObject = detectorTemplateStrategy.addIntersections(templateId, intersectionIdsRequestVO);
        case REMOVE -> resultObject = detectorTemplateStrategy.removeIntersections(templateId, intersectionIdsRequestVO);
        default -> resultObject = new DetectorTemplateManipulateResultObject(
                DetectorTemplateManipulateResultObject.StatusCode.ACTION_INVALID);
        }
        return resultObject;
    }

    /**
     * Activate or deactivate a list of templates
     * @param activeRequestVO {@link DetectorTemplateActivateRequestVO}
     * @return {@link DetectorTemplateManipulateResultObject}
     */
    @Override
    public DetectorTemplateManipulateResultObject activateTemplates(DetectorTemplateActivateRequestVO activeRequestVO) {
        return detectorTemplateStrategy.activateOrDeactivateTemplates(activeRequestVO);
    }

    @Override
    public IntersectionSearchResultObject searchTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        return detectorTemplateStrategy.searchTemplateIntersections(searchRequestVO);
    }

    @Override
    public DetectorTemplateSearchResultObject searchTemplates(DetectorTemplateSearchRequestVO requestVO) {
        return detectorTemplateStrategy.searchTemplates(requestVO);
    }

    @Override
    public DetectorTemplateProcessResultObject runTemplate(Long templateId) {
//        String email = SecurityUtils.getCurrentUserEmail();
//        Optional<StudioUserDto> userDtoOptional;
//        try {
//            userDtoOptional = studioUserService.findByEmail(email);
//        } catch (StudioException e) {
//            log.error("Error when get current user", e);
//            return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.ERROR);
//        }
//        if (userDtoOptional.isEmpty()) {
//            log.warn("Cannot find current user with email={} in Studio", email);
//            return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.UNAUTHORIZED);
//        }

        Long userId = 21L;
        TaskProgress taskProgress;
        try {
            taskProgress = jobProgressStrategy.createDetectorTemplateJob(templateId, userId);
        } catch (DetectorTemplateNotFoundException e) {
            return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.DETECTOR_TEMPLATE_NOT_FOUND);
        } catch (DetectorTemplateInvalidException e) {
            return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.DETECTOR_TEMPLATE_INVALID);
        } catch (DetectorTemplateIntersectionEmptyException e) {
            return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.INTERSECTIONS_EMPTY);
        }

        detectorTemplateStrategy.runTemplateAsync(Math.toIntExact(userId), templateId, taskProgress.getId());
        return new DetectorTemplateProcessResultObject(DetectorTemplateProcessResultObject.StatusCode.SUCCESS);
    }

    @Override
    public SimpleResultObject scanReportTemplate() {
        //find all agency
        List<Integer> allAgencyIds = agencySchemaReadService.fetchActiveAgencyIds();
        if (allAgencyIds.isEmpty()) {
           log.info("Not found any agency");
           return new SimpleResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS);
        }
       
        allAgencyIds.forEach(detectorTemplateStrategy::scanAgencyTemplatesAsync);
        
        return new SimpleResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS);
    }
}
