package com.siemens.spm.analysis.performancemetric.template.util;

import java.time.DayOfWeek;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateGeneralDataVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateSimpleDataVO;
import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateCreateRequestVO;
import com.siemens.spm.analysis.domain.PMMetadata;
import com.siemens.spm.analysis.domain.PMTemplate;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.analysis.performancemetric.util.PMMetadataBuilder;
import com.siemens.spm.analysis.summaryreport.template.util.TemplateScheduleBuilder;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import com.siemens.spm.common.util.BeanFinder;

import lombok.extern.slf4j.Slf4j;

/**
 * Helper class for build performance metric template (entity and VO object)
 *
 * <AUTHOR> Nguyen - <EMAIL>
 */
@Slf4j
public final class PMTemplateBuilder {

    private PMTemplateBuilder() {
    }

    public static PMTemplate fromCreateRequest(PMTemplateCreateRequestVO requestVO) {
        if (requestVO == null || requestVO.getGeneralDataVO().getMetaDataVO() == null) {
            throw new IllegalArgumentException("Invalid request");
        }
        PMTemplateSimpleDataVO generalData = requestVO.getGeneralDataVO();
        // validate when is
        AnalysisType analysisType = AnalysisType.getById(generalData.getMetricId());
        if (analysisType == AnalysisType.ABNORMAL_DATA) {
            DateRange dateRange = generalData.getDateRange();
            boolean valid = List.of(DateRange.Scope.RELATIVE, DateRange.Scope.FIXED).contains(dateRange.getScope());
            valid = valid &&
                    (
                            (DateRange.Scope.FIXED == dateRange.getScope() && dateRange.getTargetDate() != null && dateRange.getOffset() != null)
                                    || (DateRange.Scope.RELATIVE == dateRange.getScope() && dateRange.getOffset() != null)
                    );
            if (!valid)
                throw new IllegalArgumentException("Invalid request");
        }
        return PMTemplate.builder()
                .description(generalData.getDescription())
                .agencyId(generalData.getAgencyId())
                .metricType(AnalysisType.getById(generalData.getMetricId()))
                .timezone(generalData.getTimeZone())
                .timezoneId(generalData.getTimezoneId())
                .weekDays(generalData.getWeekDays())
                .dateRange(generalData.getDateRange())
                .metadata(PMMetadataBuilder.buildEntity(generalData.getMetaDataVO()))
                .intersectionList(IntersectionIdsVO.resolveIntersectionIdsString(requestVO.getIntersectionIdsVO()))
                .status(TemplateStatus.ACTIVE)
                .deleted(false)
                .build();

    }

    public static PMTemplateDetailVO buildTemplateDetailVO(PMTemplate template) {
        String intersectionIdsString = template.getIntersectionList();

        TemplateSchedule schedule = template.getSchedule();
        TemplateScheduleVO scheduleVO = TemplateScheduleBuilder.buildVOFromEntity(schedule);
        scheduleVO.setMailReceive(template.isMailReceive());

        return PMTemplateDetailVO.builder()
                .generalDataVO(buildGeneralDataVO(template))
                .intersectionIdsVO(IntersectionIdsVO.resolve(intersectionIdsString))
                .scheduleVO(scheduleVO)
                .build();
    }

    public static PMTemplateGeneralDataVO buildGeneralDataVO(PMTemplate template) {
        
        Set<DayOfWeek> weekDays = Set.of();
        if (template.getWeekDays() != null) {
            weekDays = new TreeSet<>(template.getWeekDays());
        }
        
        return PMTemplateGeneralDataVO.builder()
                .id(template.getId())
                .description(template.getDescription())
                .agencyId(template.getAgencyId())
                .ownerId(template.getOwnerId())
                .status(template.getStatus())
                .timeZone(template.getTimezone())
                .timezoneId(template.getTimezoneId())
                .metricId(template.getMetricType().getId())
                .metricTypeLabel(template.getMetricType().getTranslatedName())
                .dateRange(template.getDateRange())
                .weekDays(weekDays)
                .metaDataVO(PMMetadataBuilder.buildVO(template.getMetadata()))
                .ownerVO(new SimpleOwnerVO(template.getOwnerId()))
                .createdAt(template.getCreatedAt())
                .build();
    }

    public static void updateVOToEntity(PMTemplate template, PMTemplateSimpleDataVO updateRequestVO) {
        if (template == null || updateRequestVO == null || updateRequestVO.getMetaDataVO() == null) {
            throw new IllegalArgumentException();
        }

        PMMetadata metadata = PMMetadataBuilder.buildEntity(updateRequestVO.getMetaDataVO());
        DateRange dateRange = updateRequestVO.getDateRange();
        Set<DayOfWeek> weekDays = updateRequestVO.getWeekDays();

        template.setDescription(updateRequestVO.getDescription());
        template.setTimezone(updateRequestVO.getTimeZone());
        template.setMetricType(AnalysisType.getById(updateRequestVO.getMetricId()));
        template.setDateRange(dateRange);
        template.setDateRangeJson(serialize(dateRange));
        template.setWeekDays(weekDays);
        template.setWeekDaysJson(serialize(weekDays));
        template.setMetadata(metadata);
        template.setTimezoneId(updateRequestVO.getTimezoneId());
        template.setMetadataJson(serialize(metadata));
    }

    /**
     * Serialize object to json string, used to handle checked exception. If exception occur should mark as server
     * error
     *
     * @param object object need to serialize
     * @return json string represent for object
     * @throws IllegalArgumentException if you can not serialize object
     */
    private static String serialize(Object object) {
        try {
            return BeanFinder.getDefaultObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize object to json", e);

            throw new IllegalArgumentException("Failed to serialize object to json");
        }
    }

}
