package com.siemens.spm.analysis.performancemetric.template.strategy;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.siemens.spm.analysis.strategy.MOEAnalysisStrategy;
import com.siemens.spm.analysis.vo.moe.MOEAnalysisChartVO;
import com.siemens.spm.analysis.strategy.RedLightViolationStrategy;
import com.siemens.spm.analysis.vo.redlightviolation.RedLightViolationChartVO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.analysis.aggregator.AbnormalDataAggregator;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.response.AnalysisResultObject;
import com.siemens.spm.analysis.domain.PMMetadata;
import com.siemens.spm.analysis.domain.PMResult;
import com.siemens.spm.analysis.domain.PMTemplate;
import com.siemens.spm.analysis.performancemetric.template.exception.PMTemplateProcessingException;
import com.siemens.spm.analysis.strategy.AbnormalAnalysisStrategy;
import com.siemens.spm.analysis.strategy.AogAnalysisStrategy;
import com.siemens.spm.analysis.strategy.AorAnalysisStrategy;
import com.siemens.spm.analysis.strategy.AppDelayAnalysisStrategy;
import com.siemens.spm.analysis.strategy.CoordHealthAnalysisStrategy;
import com.siemens.spm.analysis.strategy.CoordinationAnalysisStrategy;
import com.siemens.spm.analysis.strategy.PedAnalysisStrategy;
import com.siemens.spm.analysis.strategy.PpAnalysisStrategy;
import com.siemens.spm.analysis.strategy.PtAnalysisStrategy;
import com.siemens.spm.analysis.strategy.QueueLengthAnalysisStrategy;
import com.siemens.spm.analysis.strategy.SplitFailureAnalysisStrategy;
import com.siemens.spm.analysis.strategy.SplitMonitorAnalysisStrategy;
import com.siemens.spm.analysis.strategy.TurningMovementAnalysisStrategy;
import com.siemens.spm.analysis.strategy.VolumeAnalysisStrategy;
import com.siemens.spm.analysis.strategy.YellowTrapStrategy;
import com.siemens.spm.analysis.util.ChartType;
import com.siemens.spm.analysis.vo.AbstractAnalysisVO;
import com.siemens.spm.analysis.vo.AogChartVO;
import com.siemens.spm.analysis.vo.AorChartVO;
import com.siemens.spm.analysis.vo.AppDelayChartVO;
import com.siemens.spm.analysis.vo.CoordinationChartVO;
import com.siemens.spm.analysis.vo.CoordinationHealthChartVO;
import com.siemens.spm.analysis.vo.DayAnalysisVO;
import com.siemens.spm.analysis.vo.MultipleDayAnalysisVO;
import com.siemens.spm.analysis.vo.PedChartVO;
import com.siemens.spm.analysis.vo.PhaseChartVO;
import com.siemens.spm.analysis.vo.PtChartVO;
import com.siemens.spm.analysis.vo.SplitFailureChartVO;
import com.siemens.spm.analysis.vo.VolumeChartDayDataVO;
import com.siemens.spm.analysis.vo.VolumeChartVO;
import com.siemens.spm.analysis.vo.VolumeMultipleDayChartVO;
import com.siemens.spm.analysis.vo.YellowTrapAnalysisVO;
import com.siemens.spm.analysis.vo.YellowTrapVO;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataAnalysisVO;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataChartVO;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataEventFilterVO;
import com.siemens.spm.analysis.vo.pp.PpChartVO;
import com.siemens.spm.analysis.vo.queuelength.QueueLengthChartVO;
import com.siemens.spm.analysis.vo.splitmonitor.SplitMonitorChartVO;
import com.siemens.spm.analysis.vo.turningmovement.TurningMovementChartVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class PMCalculatorBean implements PMCalculator {

    private static final int SECONDS_PER_HOUR = 3600;
    private static final int DAY_INTERVAL = 1;
    public static final int ABNORMAL_DATA_DAY_INTERVAL = 7;
    private static final String FAILED_TO_SERIALIZE_TURNING_MOVEMENT_ANALYSIS = "Failed to serialize turning movement analysis";

    @Autowired
    private AorAnalysisStrategy aorAnalysisStrategy;

    @Autowired
    private AogAnalysisStrategy aogAnalysisStrategy;

    @Autowired
    private PtAnalysisStrategy ptAnalysisStrategy;

    @Autowired
    private CoordinationAnalysisStrategy coordAnalysisStrategy;

    @Autowired
    private PedAnalysisStrategy pedAnalysisStrategy;

    @Autowired
    private CoordHealthAnalysisStrategy coordHealthAnalysisStrategy;

    @Autowired
    private SplitFailureAnalysisStrategy splitFailureAnalysisStrategy;

    @Autowired
    private AppDelayAnalysisStrategy appDelayAnalysisStrategy;

    @Autowired
    private QueueLengthAnalysisStrategy queueLengthAnalysisStrategy;

    @Autowired
    private VolumeAnalysisStrategy volumeAnalysisStrategy;

    @Autowired
    private YellowTrapStrategy yellowTrapStrategy;

    @Autowired
    private AbnormalAnalysisStrategy abnormalAnalysisStrategy;

    @Autowired
    private SplitMonitorAnalysisStrategy splitMonitorAnalysisStrategy;

    @Autowired
    private TurningMovementAnalysisStrategy turningMovementAnalysisStrategy;

    @Autowired
    private PpAnalysisStrategy ppAnalysisStrategy;

    @Autowired
    private RedLightViolationStrategy redLightViolationStrategy;

    @Autowired
    private MOEAnalysisStrategy moeAnalysisStrategy;

    @Override
    public PMResult process(PMTemplate template, IntersectionInternalVO intersection)
            throws PMTemplateProcessingException {
        Integer agencyId = template.getAgencyId();

        PMMetadata metadata = template.getMetadata();
        DateRange dateRange = template.getDateRange();
        Set<DayOfWeek> weekDays = template.getWeekDays();
        String timezone = template.getTimezoneId();

        Pair<LocalDate, LocalDate> datePeriod = dateRange.resolvePeriod(ZoneId.of(timezone), template.getMetricType());

        // TODO: What happened if topology change while processing ?
        String chartDataJson;
        AnalysisType metricType = template.getMetricType();
        switch (metricType) {
        case ARRIVALS_ON_RED:
            chartDataJson = aorChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case ARRIVALS_ON_GREEN:
            chartDataJson = aogChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case PHASE_TERMINATION:
            chartDataJson = ptChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case COORDINATION:
            chartDataJson = coordChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case PEDESTRIAN:
            chartDataJson = pedChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case COORDINATION_HEALTH:
            chartDataJson = coordHealthChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case SPLIT_FAILURE:
            chartDataJson = splitFailureChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case APPROACH_DELAY:
            chartDataJson = appDelayChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case QUEUE_LENGTH:
            chartDataJson = queueLengthChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case VOLUME:
            chartDataJson = volumeChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case YELLOW_TRAP_OCCURRENCES:
            chartDataJson = yellowTrapData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case ABNORMAL_DATA:
            chartDataJson = abnormalDataChart(agencyId, intersection, metadata, ZoneId.of(timezone), dateRange);
            break;
        case SPLIT_MONITOR:
            chartDataJson = splitMonitorData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case TURNING_MOVEMENT:
            chartDataJson = turningMovementChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case PREEMPTION_PRIORITY:
            chartDataJson = ppChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case RED_LIGHT_VIOLATION:
            chartDataJson = redLightViolationChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        case MOE_ANALYSIS:
            chartDataJson = moeAnalysisChartData(agencyId, intersection, metadata, datePeriod, weekDays);
            break;
        default:
            chartDataJson = null;
        }

        String intName = intersection.getName();
        return PMResult.builder()
                .intId(intersection.getId())
                .intName(intName)
                .name(String.format("%s - %s", metricType.getTranslatedName(), intName))
                .chartDataLob(chartDataJson)
                .deleted(false)
                .build();
    }

    private String abnormalDataChart(Integer agencyId,
                                     IntersectionInternalVO intersection,
                                     PMMetadata metadata,
                                     ZoneId timeZone,
                                     DateRange dateRange) throws PMTemplateProcessingException {
        String intId = intersection.getId();
        LocalDate targetDate = DateRange.Scope.FIXED == dateRange.getScope() ?
                dateRange.getTargetDate() :
                LocalDate.now(timeZone);
        int daysPeriod = dateRange.getOffset() + 1;
        LocalDate iterateDate = targetDate;

        List<AbnormalDataEventFilterVO> eventFilters = metadata.getAbnormalEventFilters();
        if (ListUtil.hasNoItem(eventFilters)) {
            throw new PMTemplateProcessingException("Missing event filter");
        }

        Map<Integer, AbnormalDataAggregator> aggData = eventFilters.stream()
                .collect(Collectors.toMap(
                        AbnormalDataEventFilterVO::getEventCode,
                        o -> new AbnormalDataAggregator(o.getEventCode(), new HashSet<>(o.getParams()),
                                o.getDeviation())));
        while (daysPeriod > 0) {
            LocalDateTime from = LocalDateTime.of(iterateDate, metadata.getFromTime());
            LocalDateTime to = LocalDateTime.of(iterateDate, metadata.getToTime());
            try {
                abnormalAnalysisStrategy.getAbnormalAnalysis(agencyId, intId, from, to, aggData);
            } catch (Exception e) {
                // nothing
            }
            iterateDate = iterateDate.minusDays(ABNORMAL_DATA_DAY_INTERVAL);
            --daysPeriod;
        }
        AbnormalDataAnalysisVO abnormalDataAnalysisVO = new AbnormalDataAnalysisVO();
        abnormalDataAnalysisVO.addChart(
                AbnormalDataChartVO.builder()
                        .targetDate(targetDate)
                        .period(dateRange.getOffset())
                        .data(abnormalAnalysisStrategy.collapse(aggData, targetDate, dateRange.getOffset()))
                        .build());
        abnormalDataAnalysisVO.setIntUUID(intId);
        abnormalDataAnalysisVO.setIntName(intId);
        abnormalDataAnalysisVO.setIsFullyApproach(true);
        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(abnormalDataAnalysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize abnormal data analysis", e);
        }
        return jsonData;
    }

    private String splitMonitorData(Integer agencyId,
                                    IntersectionInternalVO intersection,
                                    PMMetadata metadata,
                                    Pair<LocalDate, LocalDate> datePeriod,
                                    Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();
        LocalDate iterDate = fromDate;
        MultipleDayAnalysisVO<SplitMonitorChartVO> analysisVO = MultipleDayAnalysisVO.<SplitMonitorChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = fromTime.atDate(iterDate);
                LocalDateTime endTime = toTime.atDate(iterDate);

                DayAnalysisVO<SplitMonitorChartVO> dayAnalysisVO = splitMonitorDayAnalysis(agencyId, intId, startTime,
                        endTime);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }
            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }
        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize split monitor analysis", e);
        }
        return jsonData;
    }

    private DayAnalysisVO<SplitMonitorChartVO> splitMonitorDayAnalysis(Integer agencyId,
                                                                       String intId,
                                                                       LocalDateTime fromTime,
                                                                       LocalDateTime toTime) {
        DayAnalysisVO<SplitMonitorChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<SplitMonitorChartVO> resultObject = splitMonitorAnalysisStrategy
                .getSplitMonitorAnalysis(agencyId, intId, fromTime, toTime);
        AbstractAnalysisVO<SplitMonitorChartVO> analysisVO = resultObject.getData();
        if (analysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(analysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(analysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(analysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String aorChartData(Integer agencyId,
                                IntersectionInternalVO intersection,
                                PMMetadata metadata,
                                Pair<LocalDate, LocalDate> datePeriod,
                                Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<AorChartVO> analysisVO = MultipleDayAnalysisVO.<AorChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = fromTime.atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<AorChartVO> dayAnalysisVO = aorDayAnalysis(agencyId, intId, startTime, endTime,
                        binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize aor analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<AorChartVO> aorDayAnalysis(Integer agencyId,
                                                     String intId,
                                                     LocalDateTime fromTime,
                                                     LocalDateTime toTime,
                                                     Integer binSize) {
        DayAnalysisVO<AorChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<AorChartVO> resultObject = aorAnalysisStrategy
                .getAorAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<AorChartVO> aorAnalysisVO = resultObject.getData();
        if (aorAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(aorAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(aorAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(aorAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String aogChartData(Integer agencyId,
                                IntersectionInternalVO intersection,
                                PMMetadata metadata,
                                Pair<LocalDate, LocalDate> datePeriod,
                                Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<AogChartVO> analysisVO = MultipleDayAnalysisVO.<AogChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<AogChartVO> dayAnalysisVO = aogDayAnalysis(agencyId, intId, startTime, endTime,
                        binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize aog analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<AogChartVO> aogDayAnalysis(Integer agencyId,
                                                     String intId,
                                                     LocalDateTime fromTime,
                                                     LocalDateTime toTime,
                                                     Integer binSize) {
        DayAnalysisVO<AogChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<AogChartVO> resultObject = aogAnalysisStrategy
                .getAogAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<AogChartVO> aogAnalysisVO = resultObject.getData();
        if (aogAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(aogAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(aogAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(aogAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String ptChartData(Integer agencyId,
                               IntersectionInternalVO intersection,
                               PMMetadata metadata,
                               Pair<LocalDate, LocalDate> datePeriod,
                               Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<PtChartVO> analysisVO = MultipleDayAnalysisVO.<PtChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                DayAnalysisVO<PtChartVO> dayAnalysisVO = ptDayAnalysis(agencyId, intId, startTime, endTime);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize pt analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<PtChartVO> ptDayAnalysis(Integer agencyId,
                                                   String intId,
                                                   LocalDateTime fromTime,
                                                   LocalDateTime toTime) {
        DayAnalysisVO<PtChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<PtChartVO> resultObject = ptAnalysisStrategy
                .getPtAnalysis(agencyId, intId, fromTime, toTime);
        AbstractAnalysisVO<PtChartVO> ptAnalysisVO = resultObject.getData();
        if (ptAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(ptAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(ptAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(ptAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String coordChartData(Integer agencyId,
                                  IntersectionInternalVO intersection,
                                  PMMetadata metadata,
                                  Pair<LocalDate, LocalDate> datePeriod,
                                  Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<CoordinationChartVO> analysisVO = MultipleDayAnalysisVO.<CoordinationChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<CoordinationChartVO> dayAnalysisVO = coordDayAnalysis(agencyId, intId, startTime, endTime,
                        binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize coordination analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<CoordinationChartVO> coordDayAnalysis(Integer agencyId,
                                                                String intId,
                                                                LocalDateTime fromTime,
                                                                LocalDateTime toTime,
                                                                Integer binSize) {
        DayAnalysisVO<CoordinationChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<CoordinationChartVO> resultObject = coordAnalysisStrategy
                .getCoordinationAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<CoordinationChartVO> coordAnalysisVO = resultObject.getData();

        if (coordAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(coordAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(coordAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(coordAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String pedChartData(Integer agencyId,
                                IntersectionInternalVO intersection,
                                PMMetadata metadata,
                                Pair<LocalDate, LocalDate> datePeriod,
                                Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<PedChartVO> analysisVO = MultipleDayAnalysisVO.<PedChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                DayAnalysisVO<PedChartVO> dayAnalysisVO = pedDayAnalysis(agencyId, intId, startTime, endTime);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize pedestrian analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<PedChartVO> pedDayAnalysis(Integer agencyId,
                                                     String intId,
                                                     LocalDateTime fromTime,
                                                     LocalDateTime toTime) {
        DayAnalysisVO<PedChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<PedChartVO> resultObject = pedAnalysisStrategy
                .getPedestrianAnalysis(agencyId, intId, fromTime, toTime);
        AbstractAnalysisVO<PedChartVO> pedAnalysisVO = resultObject.getData();
        if (pedAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(pedAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(pedAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(pedAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String coordHealthChartData(Integer agencyId,
                                        IntersectionInternalVO intersection,
                                        PMMetadata metadata,
                                        Pair<LocalDate, LocalDate> datePeriod,
                                        Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<CoordinationHealthChartVO> analysisVO = MultipleDayAnalysisVO
                .<CoordinationHealthChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startDate = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endDate = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<CoordinationHealthChartVO> dayAnalysisVO = coordHealthDayAnalysis(agencyId, intId,
                        startDate, endDate, binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize coordination health analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<CoordinationHealthChartVO> coordHealthDayAnalysis(Integer agencyId,
                                                                            String intId,
                                                                            LocalDateTime fromTime,
                                                                            LocalDateTime toTime,
                                                                            Integer binSize) {
        DayAnalysisVO<CoordinationHealthChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<CoordinationHealthChartVO> resultObject = coordHealthAnalysisStrategy
                .getCoordinationHealthAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<CoordinationHealthChartVO> coordHealthAnalysisVO = resultObject.getData();
        if (coordHealthAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(coordHealthAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(coordHealthAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(coordHealthAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String splitFailureChartData(Integer agencyId,
                                         IntersectionInternalVO intersection,
                                         PMMetadata metadata,
                                         Pair<LocalDate, LocalDate> datePeriod,
                                         Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<SplitFailureChartVO> analysisVO = MultipleDayAnalysisVO.<SplitFailureChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startDate = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endDate = metadata.getToTime().atDate(iterDate);

                DayAnalysisVO<SplitFailureChartVO> dayAnalysisVO = splitFailureDayAnalysis(agencyId, intId, startDate,
                        endDate);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize split failure analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<SplitFailureChartVO> splitFailureDayAnalysis(Integer agencyId,
                                                                       String intId,
                                                                       LocalDateTime fromTime,
                                                                       LocalDateTime toTime) {
        DayAnalysisVO<SplitFailureChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<SplitFailureChartVO> resultObject = splitFailureAnalysisStrategy
                .getSplitFailureAnalysis(agencyId, intId, fromTime, toTime);
        AbstractAnalysisVO<SplitFailureChartVO> splitFailureAnalysisVO = resultObject.getData();
        if (splitFailureAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(splitFailureAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(splitFailureAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(splitFailureAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String appDelayChartData(Integer agencyId,
                                     IntersectionInternalVO intersection,
                                     PMMetadata metadata,
                                     Pair<LocalDate, LocalDate> datePeriod,
                                     Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<AppDelayChartVO> analysisVO = MultipleDayAnalysisVO.<AppDelayChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<AppDelayChartVO> dayAnalysisVO = appDelayDayAnalysis(agencyId, intId, startTime, endTime,
                        binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize app delay analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<AppDelayChartVO> appDelayDayAnalysis(Integer agencyId,
                                                               String intId,
                                                               LocalDateTime fromTime,
                                                               LocalDateTime toTime,
                                                               Integer binSize) {
        DayAnalysisVO<AppDelayChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<AppDelayChartVO> resultObject = appDelayAnalysisStrategy
                .getAppDelayAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<AppDelayChartVO> appDelayAnalysisVO = resultObject.getData();
        if (appDelayAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(appDelayAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(appDelayAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(appDelayAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String queueLengthChartData(Integer agencyId,
                                        IntersectionInternalVO intersection,
                                        PMMetadata metadata,
                                        Pair<LocalDate, LocalDate> datePeriod,
                                        Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<QueueLengthChartVO> analysisVO = MultipleDayAnalysisVO.<QueueLengthChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<QueueLengthChartVO> dayAnalysisVO = queueLengthDayAnalysis(agencyId, intId, startTime,
                        endTime, binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize queue length analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<QueueLengthChartVO> queueLengthDayAnalysis(Integer agencyId,
                                                                     String intId,
                                                                     LocalDateTime fromTime,
                                                                     LocalDateTime toTime,
                                                                     Integer binSize) {
        DayAnalysisVO<QueueLengthChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<QueueLengthChartVO> resultObject = queueLengthAnalysisStrategy
                .getQueueLengthAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<QueueLengthChartVO> queueLengthAnalysisVO = resultObject.getData();
        if (queueLengthAnalysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(queueLengthAnalysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(queueLengthAnalysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(queueLengthAnalysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String yellowTrapData(Integer agencyId,
                                  IntersectionInternalVO intersection,
                                  PMMetadata metadata,
                                  Pair<LocalDate, LocalDate> datePeriod,
                                  Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();
        LocalDate iterDate = fromDate;

        YellowTrapAnalysisVO analysisVO = new YellowTrapAnalysisVO();
        analysisVO.setIntUUID(intersection.getId());
        analysisVO.setIntName(intersection.getName());
        analysisVO.setIsFullyApproach(false);
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = fromTime.atDate(iterDate);
                LocalDateTime endTime = toTime.atDate(iterDate);
                YellowTrapVO result = yellowTrapStrategy.getYellowTrapAnalysis(agencyId, intId, startTime, endTime);
                analysisVO.addChart(result);
            }
            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }
        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize yellow trap analysis", e);
        }
        return jsonData;
    }

    private String volumeChartData(Integer agencyId,
                                   IntersectionInternalVO intersection,
                                   PMMetadata metadata,
                                   Pair<LocalDate, LocalDate> datePeriod,
                                   Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        Map<Integer, VolumeMultipleDayChartVO> phaseMultipleDayChartMap = new HashMap<>();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = metadata.getFromTime().atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                Map<Integer, VolumeChartVO> phaseChartMap = volumeDayAnalysis(agencyId, intId, startTime, endTime,
                        binSize);
                for (Map.Entry<Integer, VolumeChartVO> entry : phaseChartMap.entrySet()) {
                    Integer phaseNum = entry.getKey();
                    VolumeChartVO chartVO = entry.getValue();
                    int detHits = chartVO.getTotalDetHits();
                    double durationInHour = 1.0 * Duration.between(chartVO.getFromTime(), chartVO.getToTime())
                            .toSeconds() / SECONDS_PER_HOUR;

                    VolumeChartDayDataVO chartDayDataVO = VolumeChartDayDataVO.builder()
                            .date(iterDate)
                            .totalDetHits(detHits)
                            .avgDetHitPerHour((int) (detHits / durationInHour))
                            .chartDataList(chartVO.getChartDataList())
                            .build();

                    if (!phaseMultipleDayChartMap.containsKey(phaseNum)) {
                        ChartType chartType = phaseNum == 0 ? ChartType.VOLUME_ALL_PHASE : ChartType.VOLUME_ONE_PHASE;
                        VolumeMultipleDayChartVO multipleDayChartVO = VolumeMultipleDayChartVO.builder()
                                .fromTime(startTime)
                                .toTime(endTime)
                                .phase(phaseNum)
                                .chartType(chartType.getChartName())
                                .build();
                        phaseMultipleDayChartMap.put(phaseNum, multipleDayChartVO);
                    }

                    phaseMultipleDayChartMap.get(phaseNum).addDayData(chartDayDataVO);
                }

            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        DayAnalysisVO<VolumeMultipleDayChartVO> dayAnalysisVO = DayAnalysisVO.<VolumeMultipleDayChartVO>builder()
                .perfLogGapList(Collections.emptyList())
                .chartList(new ArrayList<>(phaseMultipleDayChartMap.values()))
                .isFullyApproach(true)
                .build();

        MultipleDayAnalysisVO<VolumeMultipleDayChartVO> analysisVO = MultipleDayAnalysisVO
                .<VolumeMultipleDayChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .days(List.of(dayAnalysisVO))
                .build();

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize volume analysis", e);
        }

        return jsonData;
    }

    private Map<Integer, VolumeChartVO> volumeDayAnalysis(Integer agencyId,
                                                          String intId,
                                                          LocalDateTime fromTime,
                                                          LocalDateTime toTime,
                                                          Integer binSize) {

        AnalysisResultObject<VolumeChartVO> resultObject = volumeAnalysisStrategy
                .getVolumeAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<VolumeChartVO> volumeAnalysisVO = resultObject.getData();

        if (volumeAnalysisVO == null || volumeAnalysisVO.getChartList() == null) {
            return Collections.emptyMap();
        }

        return volumeAnalysisVO.getChartList()
                .stream()
                .collect(Collectors.toMap(PhaseChartVO::getPhase, chart -> chart));
    }

    private String turningMovementChartData(Integer agencyId,
                                            IntersectionInternalVO intersection,
                                            PMMetadata metadata,
                                            Pair<LocalDate, LocalDate> datePeriod,
                                            Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<TurningMovementChartVO> analysisVO = MultipleDayAnalysisVO.<TurningMovementChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = fromTime.atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<TurningMovementChartVO> dayAnalysisVO = turningMovementDayAnalysis(agencyId, intId,
                        startTime, endTime, binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error(FAILED_TO_SERIALIZE_TURNING_MOVEMENT_ANALYSIS, e);
        }

        return jsonData;
    }

    private DayAnalysisVO<TurningMovementChartVO> turningMovementDayAnalysis(Integer agencyId,
                                                                             String intId,
                                                                             LocalDateTime fromTime,
                                                                             LocalDateTime toTime,
                                                                             Integer binSize) {
        DayAnalysisVO<TurningMovementChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<TurningMovementChartVO> resultObject = turningMovementAnalysisStrategy
                .getTurningMovementAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<TurningMovementChartVO> analysisVO = resultObject.getData();
        if (analysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(analysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(analysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(analysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String ppChartData(Integer agencyId,
                               IntersectionInternalVO intersection,
                               PMMetadata metadata,
                               Pair<LocalDate, LocalDate> datePeriod,
                               Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<PpChartVO> analysisVO = MultipleDayAnalysisVO.<PpChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTime = fromTime.atDate(iterDate);
                LocalDateTime endTime = metadata.getToTime().atDate(iterDate);

                DayAnalysisVO<PpChartVO> dayAnalysisVO = ppDayAnalysis(agencyId, intId, startTime, endTime);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize pp analysis", e);
        }

        return jsonData;
    }

    private DayAnalysisVO<PpChartVO> ppDayAnalysis(Integer agencyId,
                                                   String intId,
                                                   LocalDateTime fromTime,
                                                   LocalDateTime toTime) {
        DayAnalysisVO<PpChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<PpChartVO> resultObject = ppAnalysisStrategy
                .getPpAnalysis(agencyId, intId, fromTime, toTime);
        AbstractAnalysisVO<PpChartVO> analysisVO = resultObject.getData();
        if (analysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(analysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(analysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(analysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String redLightViolationChartData(Integer agencyId,
                                              IntersectionInternalVO intersection,
                                              PMMetadata metadata,
                                              Pair<LocalDate, LocalDate> datePeriod,
                                              Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<RedLightViolationChartVO> analysisVO = MultipleDayAnalysisVO.<RedLightViolationChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();

        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTimeDate = fromTime.atDate(iterDate);
                LocalDateTime endTimeDate = metadata.getToTime().atDate(iterDate);

                Integer binSize = metadata.getBinSize() == null ? 900 : metadata.getBinSize();
                DayAnalysisVO<RedLightViolationChartVO> dayAnalysisVO = redLightViolationDayAnalysis(agencyId, intId,
                        startTimeDate, endTimeDate, binSize);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error(FAILED_TO_SERIALIZE_TURNING_MOVEMENT_ANALYSIS, e);
        }

        return jsonData;
    }

    private DayAnalysisVO<RedLightViolationChartVO> redLightViolationDayAnalysis(Integer agencyId,
                                                                                 String intId,
                                                                                 LocalDateTime fromTime,
                                                                                 LocalDateTime toTime,
                                                                                 Integer binSize) {
        DayAnalysisVO<RedLightViolationChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<RedLightViolationChartVO> resultObject = redLightViolationStrategy
                .getRedLightViolationAnalysis(agencyId, intId, fromTime, toTime, binSize);
        AbstractAnalysisVO<RedLightViolationChartVO> analysisVO = resultObject.getData();
        if (analysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(analysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(analysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(analysisVO.getIsFullyApproach());
        }

        return dayAnalysisVO;
    }

    private String moeAnalysisChartData(Integer agencyId,
                                        IntersectionInternalVO intersection,
                                        PMMetadata metadata,
                                        Pair<LocalDate, LocalDate> datePeriod,
                                        Set<DayOfWeek> weekDays) {
        String intId = intersection.getId();
        LocalTime fromTime = metadata.getFromTime();
        LocalTime toTime = metadata.getToTime();
        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        MultipleDayAnalysisVO<MOEAnalysisChartVO> analysisVO = MultipleDayAnalysisVO
                .<MOEAnalysisChartVO>builder()
                .intUUID(intId)
                .intName(intersection.getName())
                .fromTime(fromTime)
                .toTime(toTime)
                .fromDate(fromDate)
                .toDate(toDate)
                .build();
        LocalDate iterDate = fromDate;
        while (!iterDate.isAfter(toDate)) {
            if (weekDays.contains(iterDate.getDayOfWeek())) {
                LocalDateTime startTimeDate = fromTime.atDate(iterDate);
                LocalDateTime endTimeDate = metadata.getToTime().atDate(iterDate);

                DayAnalysisVO<MOEAnalysisChartVO> dayAnalysisVO = moeAnalysisDayData(agencyId, intId,
                        startTimeDate, endTimeDate);
                analysisVO.addAnalysisDay(dayAnalysisVO);
            }

            iterDate = iterDate.plusDays(DAY_INTERVAL);
        }

        String jsonData = null;
        try {
            jsonData = BeanFinder.getDefaultObjectMapper().writeValueAsString(analysisVO);
        } catch (JsonProcessingException e) {
            log.error(FAILED_TO_SERIALIZE_TURNING_MOVEMENT_ANALYSIS, e);
        }
        return jsonData;
    }

    private DayAnalysisVO<MOEAnalysisChartVO> moeAnalysisDayData(Integer agencyId,
                                                                 String intId,
                                                                 LocalDateTime fromTime,
                                                                 LocalDateTime toTime) {
        DayAnalysisVO<MOEAnalysisChartVO> dayAnalysisVO = new DayAnalysisVO<>();
        dayAnalysisVO.setDate(fromTime.toLocalDate());

        AnalysisResultObject<MOEAnalysisChartVO> resultObject = moeAnalysisStrategy.getMOEAnalysis(
                agencyId, intId, fromTime, toTime
        );
        AbstractAnalysisVO<MOEAnalysisChartVO> analysisVO = resultObject.getData();
        if (analysisVO != null) {
            dayAnalysisVO.setPerfLogGapList(analysisVO.getPerfLogGapList());
            dayAnalysisVO.setChartList(analysisVO.getChartList());
            dayAnalysisVO.setIsFullyApproach(analysisVO.getIsFullyApproach());
        }
        return dayAnalysisVO;
    }
}
