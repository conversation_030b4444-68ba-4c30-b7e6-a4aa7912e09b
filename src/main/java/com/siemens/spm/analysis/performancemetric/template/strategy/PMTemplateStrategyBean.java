package com.siemens.spm.analysis.performancemetric.template.strategy;

import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.AnalysisMetadataVO;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.TemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateGeneralDataVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateSimpleDataVO;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.performancemetric.PMTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.response.AnalysisMetadataResultObject;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.TemplateScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateGeneralDataResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateManipulateResultObject.PMTemplateManipulateStatusCode;
import com.siemens.spm.analysis.api.vo.response.performancemetric.PMTemplateSearchResultObject;
import com.siemens.spm.analysis.config.ReportTemplateConfig;
import com.siemens.spm.analysis.domain.PMResult;
import com.siemens.spm.analysis.domain.PMResultSharedData;
import com.siemens.spm.analysis.domain.PMTemplate;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.analysis.factory.AnalysisMetadataVOFactory;
import com.siemens.spm.analysis.factory.TimezoneFactory;
import com.siemens.spm.analysis.performancemetric.template.exception.PMTemplateNotFoundException;
import com.siemens.spm.analysis.performancemetric.template.exception.PMTemplateProcessingException;
import com.siemens.spm.analysis.performancemetric.template.util.PMTemplateBuilder;
import com.siemens.spm.analysis.performancemetric.template.util.PMTemplateSearchHelper;
import com.siemens.spm.analysis.repository.PMResultRepository;
import com.siemens.spm.analysis.repository.PMResultSharedDataRepository;
import com.siemens.spm.analysis.repository.PMTemplateRepository;
import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
import com.siemens.spm.analysis.repository.filterdata.PMTemplateFilterData;
import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
import com.siemens.spm.analysis.summaryreport.template.util.TemplateScheduleBuilder;
import com.siemens.spm.analysis.summaryreport.util.TemplateScheduleHelper;
import com.siemens.spm.analysis.vo.abnormaldata.AbnormalDataEventFilterVO;
import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.constant.TimeConstants;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.IntersectionScope;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.IntersectionSearchResponseDataVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.common.util.NameUtil;
import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.perflog.vo.PerfLogEventVO;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class PMTemplateStrategyBean implements PMTemplateStrategy {

    private final ReportTemplateConfig reportTemplateConfig;

    private final IntersectionStrategy intersectionStrategy;

    private final ReportNotificationStrategy reportNotificationStrategy;

    private final TaskProgressRetriever taskProgressRetriever;

    private final PMCalculator pmCalculator;

    private final StudioUserService studioUserService;

    private final PMTemplateRepository pmTemplateRepository;

    private final PMResultRepository pmResultRepository;

    private final PMResultSharedDataRepository pmResultSharedDataRepository;

    private final TemplateScheduleRepository scheduleRepository;

    private final TimezoneFactory timezoneFactory;

    public PMTemplateStrategyBean(TimezoneFactory timezoneFactory,
                                  TemplateScheduleRepository scheduleRepository,
                                  PMResultSharedDataRepository pmResultSharedDataRepository,
                                  PMResultRepository pmResultRepository,
                                  PMTemplateRepository pmTemplateRepository,
                                  StudioUserService studioUserService,
                                  PMCalculator pmCalculator,
                                  TaskProgressRetriever taskProgressRetriever,
                                  ReportNotificationStrategy reportNotificationStrategy,
                                  IntersectionStrategy intersectionStrategy,
                                  ReportTemplateConfig reportTemplateConfig) {
        this.timezoneFactory = timezoneFactory;
        this.scheduleRepository = scheduleRepository;
        this.pmResultSharedDataRepository = pmResultSharedDataRepository;
        this.pmResultRepository = pmResultRepository;
        this.pmTemplateRepository = pmTemplateRepository;
        this.studioUserService = studioUserService;
        this.pmCalculator = pmCalculator;
        this.taskProgressRetriever = taskProgressRetriever;
        this.reportNotificationStrategy = reportNotificationStrategy;
        this.intersectionStrategy = intersectionStrategy;
        this.reportTemplateConfig = reportTemplateConfig;
    }

    private static final String STUDIO_USER_ERROR_MSG = "Error when get user by id from Studio";

    @Transactional
    @Override
    public PMTemplateDetailResultObject create(PMTemplateCreateRequestVO requestVO) {
        if (requestVO == null) {
            throw new IllegalArgumentException("Invalid request");
        }

        PMTemplateSimpleDataVO generalData = requestVO.getGeneralDataVO();

        String metricTypeId = generalData.getMetricId();
        AnalysisType analysisType = AnalysisType.getById(metricTypeId);
        if (analysisType == null) {
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.INVALID_METRIC_TYPE);
        }

        Integer agencyId = generalData.getAgencyId();
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error("Error when get current user", e);
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn("Cannot find current user with email={} in Studio", email);
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.UNAUTHORIZED);
        }

        Long userId = Long.valueOf(userDtoOptional.get().getId());

        // Verify agency
        if (agencyId == null) {
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.MISSING_AGENCY_ID);
        }

        // default get timezone of agency if it couldn't receive timezone from Frontend side
        String timeZone = requestVO.getGeneralDataVO().getTimezoneId();
        if (StringUtils.isEmpty(timeZone)) {
            requestVO.getGeneralDataVO().setTimezoneId(timezoneFactory.validateAndResolveTimezone(timeZone, agencyId));
        }
        requestVO.getGeneralDataVO().setTimeZone(DateTimeUtils.getZoneOffSet(timeZone));

        DateRange dateRange = generalData.getDateRange();
        TemplateScheduleVO scheduleVO = requestVO.getScheduleVO();

        // Validate date range scope vs schedule scope comparable
        if (dateRange.getScope() == DateRange.Scope.SPECIFIC
                && scheduleVO.getScope() != ScheduleScope.MANUAL
                && scheduleVO.getScope() != ScheduleScope.ONCE) {
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.SCHEDULE_IS_INVALID);
        }
        if (!isValidDateRange(dateRange)) {
            return PMTemplateDetailResultObject.error(PMTemplateDetailResultObject.StatusCode.INVALID_DATE_RANGE);
        }

        if (analysisType == AnalysisType.ABNORMAL_DATA && !isValidAbnormalDataFilter(
                generalData.getMetaDataVO().getAbnormalDataFilter())) {
            return PMTemplateDetailResultObject.error(
                    PMTemplateDetailResultObject.StatusCode.INVALID_ABNORMAL_DATA_FILTER);
        }

        // Verify intersections
        List<String> intersectionIdsList = IntersectionIdsVO
                .resolveIntersectionIdsList(requestVO.getIntersectionIdsVO());
        if (!intersectionIdsList.isEmpty()) {
            // TODO
//            IntersectionsInAgencyVerifyResultObject.StatusCode verifyIntersectionsResult = agencyStrategy
//                    .verifyIntersectionsInAgency(agencyId, intersectionIdsList);
//
//            if (verifyIntersectionsResult != IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS) {
//                return PMTemplateDetailResultObject
//                        .error(PMTemplateDetailResultObject.StatusCode.valueOf(verifyIntersectionsResult.name()));
//            }
        }

        PMTemplate template = PMTemplateBuilder.fromCreateRequest(requestVO);
        template.setAgencyId(agencyId);
        template.setOwnerId(userId);

        template.setMailReceive(scheduleVO.getMailReceive());

        TemplateSchedule schedule = TemplateScheduleBuilder.buildEntityFromVO(requestVO.getScheduleVO());
        scheduleRepository.saveAndFlush(schedule);
        template.setSchedule(schedule);
        pmTemplateRepository.save(template);

        // Process build response and send noti
        PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, userId, TemplateConstant.ActionOnTemplate.CREATED);

        return new PMTemplateDetailResultObject(templateDetailVO, PMTemplateDetailResultObject.StatusCode.SUCCESS);
    }

    private boolean isValidDateRange(DateRange dateRange) {
        if (dateRange == null || dateRange.getScope() == null) {
            return false;
        }

        DateRange.Scope scope = dateRange.getScope();
        if (scope == DateRange.Scope.SPECIFIC) {
            LocalDate startDate = dateRange.getStartDate();
            LocalDate endDate = dateRange.getEndDate();
            if (startDate == null || endDate == null) {
                return false;
            }
            long dayDuration = Duration.between(startDate.atStartOfDay(), endDate.plusDays(1).atStartOfDay()).toDays();

            return dayDuration >= TimeConstants.MIN_DAY_PER_WEEK && dayDuration <= TimeConstants.MAX_DAY_PER_WEEK;
        }

        if (scope == DateRange.Scope.RELATIVE) {
            DateRange.Unit unit = dateRange.getUnit();
            Integer offset = dateRange.getOffset();

            if (unit == null || offset == null || offset < TimeConstants.MIN_DAY_PER_WEEK) {
                return false;
            }
            return List.of(DateRange.Unit.WEEK, DateRange.Unit.MONTH)
                    .contains(unit) || (unit == DateRange.Unit.DAY && offset <= TimeConstants.MAX_DAY_PER_WEEK);
        }

        if (scope == DateRange.Scope.FIXED) {
            DateRange.Unit unit = dateRange.getUnit();
            Integer offset = dateRange.getOffset();
            LocalDate targetDate = dateRange.getTargetDate();
            return !(unit == null || offset == null || targetDate == null);
        }

        return false;
    }

    private boolean isValidAbnormalDataFilter(List<AbnormalDataEventFilterVO> eventFilters) {
        if (eventFilters == null) {
            return false;
        }

        for (AbnormalDataEventFilterVO filterVO : eventFilters) {
            Integer eventCode = filterVO.getEventCode();
            PerfLogEventVO.Event event = PerfLogEventVO.Event.of(eventCode);
            if (event == null) {
                return false;
            }

            Double deviation = filterVO.getDeviation();
            if (deviation != null && deviation < 0) {
                return false;
            }
        }

        return true;
    }

    @Override
    public IntersectionSearchResultObject searchAvailableTemplateInterSections(
            TemplateIntersectionSearchRequestVO searchRequestVO) {
        Integer agencyId;
        Long templateId = searchRequestVO.getTemplateId();

        List<String> currentIntIdsList;
        if (templateId != null) {
            Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
            if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
                return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
            }

            PMTemplate template = templateOptional.get();
            agencyId = template.getAgencyId();
            String currentIntIdsString = template.getIntersectionList();
            if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(currentIntIdsString)) {
                String[] intIdsArr = currentIntIdsString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
                currentIntIdsList = Arrays.asList(intIdsArr);
            } else {
                IntersectionSearchResponseDataVO responseDataVO = IntersectionSearchResponseDataVO.builder()
                        .intersections(new ArrayList<>())
                        .totalCount(0L)
                        .scope(IntersectionScope.SPECIFIC_INTERSECTIONS)
                        .build();

                return IntersectionSearchResultObject.success(responseDataVO);
            }
        } else {
            agencyId = searchRequestVO.getAgencyId();

            currentIntIdsList = new ArrayList<>();
        }

        // Append excluded intersection ids from request
        String[] excludeIntIds = searchRequestVO.getExcludeIntIds();
        if (excludeIntIds != null && excludeIntIds.length > 0) {
            currentIntIdsList = Stream.concat(currentIntIdsList.stream(), Arrays.stream(excludeIntIds))
                    .distinct()
                    .toList();
        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .exclusionaryIds(currentIntIdsList)
                .shouldPaginate(searchRequestVO.getShouldPaginate())
                .page(searchRequestVO.getPage())
                .size(searchRequestVO.getSize())
                .status(IntersectionStatus.AVAILABLE.getInsight()) // Just returned enabled intersection
                .text(searchRequestVO.getText())
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .build();
        IntersectionSearchResponseDataVO responseVO = intersectionStrategy.searchIntersectionsInternal(searchRequest);

        return IntersectionSearchResultObject.success(responseVO);
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject updateScheduleData(Long templateId, TemplateScheduleVO templateScheduleVO) {
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new PMTemplateManipulateResultObject(PMTemplateManipulateStatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();
        TemplateSchedule schedule = template.getSchedule();
        TemplateScheduleBuilder.updateVOToEntity(schedule, templateScheduleVO);
        scheduleRepository.save(schedule);

        template.setMailReceive(templateScheduleVO.getMailReceive());
        pmTemplateRepository.save(template);

        PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return new PMTemplateManipulateResultObject(PMTemplateManipulateStatusCode.SUCCESS);
    }

    @Override
    public PMTemplateGeneralDataResultObject getGeneralData(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException();
        }
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new PMTemplateGeneralDataResultObject(PMTemplateGeneralDataResultObject.StatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();
        PMTemplateGeneralDataVO generalDataVO = PMTemplateBuilder.buildGeneralDataVO(template);
        updateOwnerData(generalDataVO.getOwnerVO());

        return PMTemplateGeneralDataResultObject.success(generalDataVO);
    }

    @Override
    public TemplateScheduleResultObject getScheduleData(Long templateId) {
        if (templateId == null) {
            throw new IllegalArgumentException();
        }
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return TemplateScheduleResultObject.error(TemplateScheduleResultObject.StatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();
        TemplateScheduleVO scheduleVO = TemplateScheduleBuilder.buildVOFromEntity(template.getSchedule());
        scheduleVO.setMailReceive(template.isMailReceive());

        return TemplateScheduleResultObject.success(scheduleVO);
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject updateGeneralData(Long templateId,
                                                              PMTemplateSimpleDataVO updateRequestVO) {
        if (updateRequestVO == null || !updateRequestVO.isValid()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.INVALID_REQUEST);
        }

        if (!isValidDateRange(updateRequestVO.getDateRange())) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.INVALID_DATE_RANGE);
        }

        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.NOT_FOUND);
        }
        PMTemplate template = templateOptional.get();
        updateScheduleDataToManualIfNeed(template, updateRequestVO.getDateRange());

        String timeZone = updateRequestVO.getTimezoneId();
        if (StringUtils.isEmpty(timeZone)) {
            updateRequestVO.setTimeZone(timezoneFactory.validateAndResolveTimezone(timeZone, updateRequestVO.getAgencyId()));
        }
        updateRequestVO.setTimeZone(DateTimeUtils.getZoneOffSet(timeZone));

        PMTemplateBuilder.updateVOToEntity(template, updateRequestVO);

        pmTemplateRepository.save(template);

        PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
    }

    @Override
    public IntersectionSearchResultObject searchTemplateIntersections(
            TemplateIntersectionSearchRequestVO searchRequestVO) {
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(searchRequestVO.getTemplateId());
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId())
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .shouldPaginate(true)
                .page(searchRequestVO.getPage())
                .size(searchRequestVO.getSize())
                .status(IntersectionStatus.AVAILABLE.getInsight()) // Just return enabled intersection
                .text(searchRequestVO.getText())
                .build();

        IntersectionSearchResponseDataVO responseDataVO = intersectionStrategy
                .searchIntersectionsInternal(template.getIntersectionList(), searchRequest);

        return IntersectionSearchResultObject.success(responseDataVO);
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject addIntersections(Long templateId,
                                                             IntersectionIdsRequestVO intersectionIdsRequestVO) {
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();
        List<String> intersectionIdsListRequest = IntersectionIdsVO
                .resolveIntersectionIdsList(intersectionIdsRequestVO);

        // Verify intersections
        if (!intersectionIdsListRequest.isEmpty()) {
            // TODO
//            IntersectionsInAgencyVerifyResultObject.StatusCode verifyIntersectionsResult = agencyStrategy
//                    .verifyIntersectionsInAgency(agencyId, intersectionIdsListRequest);
//            if (verifyIntersectionsResult != IntersectionsInAgencyVerifyResultObject.StatusCode.SUCCESS) {
//                return PMTemplateManipulateResultObject
//                        .build(PMTemplateManipulateStatusCode.valueOf(verifyIntersectionsResult.name()));
//            }
        }

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (intersectionScope == IntersectionScope.ALL_INTERSECTIONS) {
            template.setIntersectionList(IntersectionConstants.ALL_INTERSECTION_INDICATOR);
            pmTemplateRepository.save(template);

            PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.UPDATE);

            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
        } else if (intersectionScope == IntersectionScope.SPECIFIC_INTERSECTIONS) {
            // Check case add new when current scope is ALL_INTERSECTIONS first
            if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(template.getIntersectionList())) {
                return PMTemplateManipulateResultObject
                        .build(PMTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
            }

            String[] currentIntIdsArr = template.getIntersectionList()
                    .split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
            List<String> newIntersectionIdsList = new ArrayList<>(Arrays.asList(currentIntIdsArr));
            for (String uuid : intersectionIdsListRequest) {
                if (!newIntersectionIdsList.contains(uuid)) {
                    newIntersectionIdsList.add(uuid);
                }
            }
            String newIntersectionIdsString = newIntersectionIdsList.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

            template.setIntersectionList(newIntersectionIdsString);
            pmTemplateRepository.save(template);

            PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
            notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.UPDATE);

            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
        }

        return PMTemplateManipulateResultObject
                .build(PMTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject removeIntersections(Long templateId,
                                                                IntersectionIdsRequestVO intersectionIdsRequestVO) {
        // Verify template exist or not
        Optional<PMTemplate> templateOptional = pmTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.NOT_FOUND);
        }

        PMTemplate template = templateOptional.get();

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (intersectionScope == null) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.INTERSECTION_SCOPE_INVALID);
        }

        String newIntersectionIdsString;

        switch (intersectionScope) {
            case ALL_INTERSECTIONS -> newIntersectionIdsString = "";
            case SPECIFIC_INTERSECTIONS -> {
                List<String> intIdsListRequest = intersectionIdsRequestVO.getUuids();
                if (ListUtil.hasNoItem(intIdsListRequest)) {
                    newIntersectionIdsString = template.getIntersectionList();
                } else {
                    newIntersectionIdsString = getAllIntersectionIds(template)
                            .stream()
                            .filter(uuid -> !intIdsListRequest.contains(uuid))
                            .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
                }
            }
            default -> newIntersectionIdsString = template.getIntersectionList();
        }

        template.setIntersectionList(newIntersectionIdsString);
        pmTemplateRepository.save(template);

        PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(template);
        notifyOwnerAboutTemplateChange(templateDetailVO, template.getOwnerId(),
                TemplateConstant.ActionOnTemplate.UPDATE);

        return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
    }

    private List<String> getAllIntersectionIds(PMTemplate template) {
        final String intersectionIdString = Objects.requireNonNull(template.getIntersectionList(),
                "Intersection list is null");
        if (!intersectionIdString.equals(IntersectionConstants.ALL_INTERSECTION_INDICATOR)) {
            return Arrays.asList(intersectionIdString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId())
                .shouldPaginate(false)
                .build();

        return intersectionStrategy.getIntersectionsByFilterInternal(searchRequest)
                .getIntersections()
                .stream()
                .map(IntersectionInternalVO::getId)
                .toList();
    }

    @Override
    public PMTemplateSearchResultObject search(PMTemplateSearchRequestVO searchRequestVO) {
        if (searchRequestVO == null) {
            throw new IllegalArgumentException();
        }

        Pair<List<PMTemplate>, Long> templatesAndCountPair;
        try {
            templatesAndCountPair = getTemplateAndCountPairFromDB(searchRequestVO);
        } catch (InvalidSortColumnException ex) {
            return PMTemplateSearchResultObject.error(PMTemplateSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            return PMTemplateSearchResultObject.error(PMTemplateSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }

        List<PMTemplate> templates = templatesAndCountPair.getFirst();
        Long totalCount = templatesAndCountPair.getSecond();

        if (templates.isEmpty()) {
            return PMTemplateSearchResultObject.success(PMTemplateSearchResultObject.ResponseData.empty());
        }

        List<PMTemplateGeneralDataVO> templateVOList = templates.stream()
                .map(PMTemplateBuilder::buildGeneralDataVO)
                .toList();
        updateOwnerData(templateVOList);

        PMTemplateSearchResultObject.ResponseData responseData = PMTemplateSearchResultObject.ResponseData.builder()
                .templates(templateVOList)
                .totalCount(totalCount)
                .build();

        return PMTemplateSearchResultObject.success(responseData);
    }

    private Pair<List<PMTemplate>, Long> getTemplateAndCountPairFromDB(PMTemplateSearchRequestVO searchRequestVO) {
        OrderSpecifier<String>[] orderSpecifiers = PMTemplateSearchHelper
                .createOrderBy(searchRequestVO.getOrderByColumns());

        PMTemplateFilterData filterData = PMTemplateFilterData.builder()
                .agencyId(searchRequestVO.getAgencyId())
                .text(searchRequestVO.getText())
                .metricType(AnalysisType.getById(searchRequestVO.getMetricTypeId()))
                .createdAtFrom(searchRequestVO.getCreatedAtFrom())
                .createdAtTo(searchRequestVO.getCreatedAtTo())
                .status(searchRequestVO.getStatus())
                .ownerId(searchRequestVO.getOwnerId())
                .build();

        List<PMTemplate> templates = pmTemplateRepository.findPageByFilter(
                filterData,
                orderSpecifiers,
                searchRequestVO.getPage(),
                searchRequestVO.getSize());

        Long totalCount = pmTemplateRepository.countTotalByFilter(filterData);

        return Pair.of(templates, totalCount);
    }

    private void updateOwnerData(List<PMTemplateGeneralDataVO> templateGeneralDataVOList) {
        List<Integer> ownerIds = templateGeneralDataVOList.stream()
                .map(PMTemplateGeneralDataVO::getOwnerId)
                .filter(Objects::nonNull)
                .map(Long::intValue)
                .distinct()
                .toList();

        Map<Integer, StudioUserDto> ownerMap;
        try {
            ownerMap = studioUserService.getMapUsersByIds(ownerIds);
        } catch (StudioException e) {
            log.error("Error when get users from Studio", e);
            ownerMap = Collections.emptyMap();
        }

        for (PMTemplateGeneralDataVO templateVO : templateGeneralDataVOList) {
            Long userId = templateVO.getOwnerId();
            if (userId == null) {
                continue;
            }

            SimpleOwnerVO ownerVO = new SimpleOwnerVO();
            ownerVO.setId(userId);

            StudioUserDto userDto = ownerMap.get(userId.intValue());
            if (userDto != null) {
                ownerVO.setName(NameUtil.usFullName(userDto.getFirstName(), userDto.getLastName()));
                ownerVO.setEmail(userDto.getEmail());
            } else {
                ownerVO.setName(CommonConstants.UNKNOWN_USER_NAME);
                ownerVO.setEmail(CommonConstants.UNKNOWN_USER_EMAIL);
            }
            templateVO.setOwnerVO(ownerVO);
        }
    }

    private void updateOwnerData(SimpleOwnerVO ownerVO) {
        if (ownerVO == null) {
            throw new IllegalArgumentException();
        }

        try {
            studioUserService.findById(Math.toIntExact(ownerVO.getId()))
                    .ifPresent(userDto -> {
                        ownerVO.setEmail(userDto.getEmail());
                        ownerVO.setName(NameUtil.usFullName(userDto.getFirstName(), userDto.getLastName()));
                    });
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
        }
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject softDeleteTemplates(List<Long> templateIds) {
        List<PMTemplate> templates = pmTemplateRepository.findAllByIdIn(templateIds);
        if (templates.isEmpty()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.NOT_FOUND);
        }

        // Filter template which delete field is false
        List<PMTemplate> templatesToDelete = templates.stream()
                .filter(template -> !template.isDeleted())
                .toList();

        // Update delete field to soft delete
        for (PMTemplate pmTemplate : templatesToDelete) {
            pmTemplate.setDeleted(true);
            pmTemplateRepository.save(pmTemplate);

            // Send notification to user
            PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(pmTemplate);
            notifyOwnerAboutTemplateChange(templateDetailVO, pmTemplate.getOwnerId(),
                    TemplateConstant.ActionOnTemplate.DELETE);
        }
        return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
    }

    @Transactional
    @Override
    public PMTemplateManipulateResultObject activeTemplates(List<Long> templateIds, TemplateStatus status) {
        if (templateIds == null || status == null) {
            throw new IllegalArgumentException();
        }

        List<PMTemplate> templates = pmTemplateRepository.findAllByIdIn(templateIds);
        if (templates.isEmpty()) {
            return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.NOT_FOUND);
        }

        //Filter template which status is difference from request status
        List<PMTemplate> templatesToUpdate = templates.stream()
                .filter(template -> !template.isDeleted())
                .filter(template -> template.getStatus() != status)
                .toList();

        for (PMTemplate pmTemplate : templatesToUpdate) {
            pmTemplate.setStatus(status);
            pmTemplateRepository.save(pmTemplate);

            // Send notification to user
            TemplateConstant.ActionOnTemplate action = switch (status) {
                case ACTIVE -> TemplateConstant.ActionOnTemplate.ACTIVATE;
                case INACTIVE -> TemplateConstant.ActionOnTemplate.DEACTIVATE;
            };

            PMTemplateDetailVO templateDetailVO = PMTemplateBuilder.buildTemplateDetailVO(pmTemplate);
            notifyOwnerAboutTemplateChange(templateDetailVO, pmTemplate.getOwnerId(), action);
        }

        return PMTemplateManipulateResultObject.build(PMTemplateManipulateStatusCode.SUCCESS);
    }

    @Transactional
    @Override
    public void runTemplateAsync(Long userId,
                                 Long templateId,
                                 Long jobId,
                                 List<IntersectionInternalVO> intersections) {
        PMTemplate template;
        PMResultSharedData resultSharedData;
        try {
            Optional<PMTemplate> templateOpt = pmTemplateRepository.findById(templateId);
            if (templateOpt.isEmpty()) {
                throw new PMTemplateProcessingException("Can not find template, templateId=" + templateId);
            }

            template = templateOpt.get();

            resultSharedData = processTemplate(template, userId, intersections);
        } catch (PMTemplateProcessingException e) {
            log.error("Can not process template, templateId=" + templateId, e);

            taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.ERROR, null);

            return;
        } catch (Exception e) {
            log.error("Unexpected exception occur while process template, templateId=" + templateId, e);

            taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.ERROR, null);
            return;
        }

        // Completed job progress
        taskProgressRetriever.updateTaskProgress(jobId, TaskStatus.COMPLETED, null);

        // Send notification and email
        notifyRunnerAboutResultCreated(userId, resultSharedData, template.isMailReceive());
    }

    @Transactional
    @Override
    public void scanTemplatesAsync() {
        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active templates
        List<PMTemplate> templates = pmTemplateRepository.findAllByStatusAndDeleted(TemplateStatus.ACTIVE, false);
        if (ListUtil.hasNoItem(templates)) {
            log.debug("Have no any active template. So have no template triggered");
            return;
        }

        // Find all the templates that can be process
        List<PMTemplate> candidateTemplates = templates.stream()
                .filter(template -> TemplateScheduleHelper
                        .isCandidateInTime(template.getSchedule(), template.getTimezoneId(), now, offset))
                .toList();

        if (!candidateTemplates.isEmpty()) {
            List<Long> templateIds = candidateTemplates.stream()
                    .map(PMTemplate::getId)
                    .toList();
            log.debug("Found {} templates can be processed. Templates: {}", candidateTemplates.size(), templateIds);
            for (PMTemplate template : candidateTemplates) {
                try {
                    // Process templates. Should mark owner id as null (own by System)
                    PMResultSharedData resultSharedData = processTemplate(template, null);

                    notifyRunnerAboutResultCreated(template.getOwnerId(), resultSharedData, template.isMailReceive());
                } catch (PMTemplateProcessingException e) {
                    log.error("Process performance metric template, templateId=" + template.getId() + " failed!", e);
                } catch (Exception e) {
                    log.error("Unexpected errors while process template, templateId=" + template.getId(), e);
                }
            }
        }
    }

    @Override
    public AnalysisMetadataResultObject getAllAnalysisTypes() {
        AnalysisMetadataResultObject resultObject;

        List<AnalysisMetadataVO> analysisVOList = AnalysisMetadataVOFactory.analysisTypesForScheduleAnalysis();
        for (AnalysisMetadataVO vo : analysisVOList) {
            vo.acceptTranslator(BeanFinder.getDefaultMessageService());
        }

        analysisVOList.sort(Comparator.comparing(AnalysisMetadataVO::getName));

        AnalysisMetadataResultObject.ResponseData data = AnalysisMetadataResultObject.ResponseData.builder()
                .analyses(analysisVOList).build();

        resultObject = new AnalysisMetadataResultObject(data, AnalysisMetadataResultObject.StatusCode.SUCCESS);

        return resultObject;
    }

    /**
     * Update schedule data of a specific pm template to MANUAL if:
     * <p>
     * 1. Current date range scope is RELATIVE
     * <p>
     * 2. New date range scope is SPECIFIC
     * <p>
     * 3. Current schedule scope is not MANUAL
     *
     * @param template  current pm template
     * @param dateRange new date range
     */
    private void updateScheduleDataToManualIfNeed(PMTemplate template, DateRange dateRange) {
        DateRange currentDateRange = template.getDateRange();
        TemplateSchedule templateSchedule = template.getSchedule();

        if (currentDateRange.getScope() == DateRange.Scope.RELATIVE
                && dateRange.getScope() == DateRange.Scope.SPECIFIC
                && templateSchedule.getScope() != ScheduleScope.MANUAL) {
            templateSchedule.setScope(ScheduleScope.MANUAL);
            templateSchedule.setTime(null);
            templateSchedule.setValue(null);
            templateSchedule.setMetricJson(null);

            scheduleRepository.save(templateSchedule);
            log.debug("Successful update template schedule to MANUAL, templateId = {}", template.getId());
        }
    }

    private PMResultSharedData processTemplate(PMTemplate template, Long userId) throws PMTemplateProcessingException {
        IntersectionInternalListVO intersectionListVO = findAllIntersectionsInTemplate(template);

        if (intersectionListVO == null || ListUtil.hasNoItem(intersectionListVO.getIntersections())) {
            String error = String.format("Template doesn't have any intersections, templateId=%d", template.getId());
            throw new PMTemplateProcessingException(error);
        }
        return processTemplate(template, userId, intersectionListVO.getIntersections());
    }

    private PMResultSharedData processTemplate(PMTemplate template,
                                               Long userId,
                                               List<IntersectionInternalVO> intersections)
            throws PMTemplateProcessingException {

        // Process intersection one by one
        Set<PMResult> results = new HashSet<>();
        for (IntersectionInternalVO intersection : intersections) {
            PMResult result = pmCalculator.process(template, intersection);
            result.setCreatedAt(Timestamp.from(Instant.now()));
            results.add(result);
        }
        Pair<LocalDate, LocalDate> dateDate = template.getDateRange()
                .resolvePeriod(ZoneId.of(template.getTimezoneId()), template.getMetricType());
        PMResultSharedData resultSharedData = PMResultSharedData.builder()
                .agencyId(template.getAgencyId())
                .description(template.getDescription())
                .metricType(template.getMetricType())
                .timezone(template.getTimezoneId())
                .fromDate(dateDate.getFirst())
                .toDate(dateDate.getSecond())
                .weekDays(template.getWeekDays())
                .metadata(template.getMetadata())
                .ownerId(userId)
                .results(results)
                .build();

        if (!results.isEmpty()) {
            pmResultSharedDataRepository.save(resultSharedData);

            for (PMResult result : results) {
                result.setSharedData(resultSharedData);
                result.setTemplate(template);
            }
            pmResultRepository.saveAll(results);
        }

        return resultSharedData;
    }

    public IntersectionInternalListVO findAllIntersectionsInTemplate(Long templateId)
            throws PMTemplateNotFoundException {
        Optional<PMTemplate> templateOpt = pmTemplateRepository.findById(templateId);
        if (templateOpt.isEmpty()) {
            throw new PMTemplateNotFoundException("Template was not found, templateId=" + templateId);
        }
        return findAllIntersectionsInTemplate(templateOpt.get());
    }

    private IntersectionInternalListVO findAllIntersectionsInTemplate(PMTemplate template) {

        String intersectionListStr = template.getIntersectionList();
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId())
                .shouldPaginate(false)
                .status(IntersectionStatus.AVAILABLE.getInsight()) // Exclude Disabled intersection
                .build();

        if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionListStr)) {
            String[] currentIntIdsArr = template.getIntersectionList()
                    .split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
            searchRequest.setIntersectionIds(Arrays.asList(currentIntIdsArr));
        }

        return intersectionStrategy.getIntersectionsByFilterInternal(searchRequest);
    }

    private void notifyRunnerAboutResultCreated(Long userId, PMResultSharedData resultSharedData, boolean mailReceive) {
        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(userId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found runner's information of result!");
            return;
        }

        reportNotificationStrategy.sendPMResultNotiAsync(resultSharedData, List.of(userOptional.get()), mailReceive);
    }

    private void notifyOwnerAboutTemplateChange(PMTemplateDetailVO templateDetailVO,
                                                Long ownerId,
                                                TemplateConstant.ActionOnTemplate action) {
        // TODO: Consider disable this feature when owner modify template
        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(ownerId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found owner's information of pm template!");
            return;
        }

        reportNotificationStrategy.sendPMTemplateNotiAsync(templateDetailVO, List.of(userOptional.get()), action);
    }

    @Override
    @Transactional
    @AgencyAware(agencyId = "[0]")
    public void scanAgencyTemplatesAsync(int agencyId) {
        log.info("Scan scheduled performance-metric templates for agency: {}", agencyId);

        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active templates
        List<PMTemplate> templates = pmTemplateRepository.findAllByStatusAndDeleted(TemplateStatus.ACTIVE, false);
        if (ListUtil.hasNoItem(templates)) {
            log.debug("Have no any active template. So have no template triggered");
            return;
        }

        // Find all the templates that can be process
        List<PMTemplate> candidateTemplates = templates.stream()
                .filter(template -> TemplateScheduleHelper
                        .isCandidateInTime(template.getSchedule(), template.getTimezoneId(), now, offset))
                .toList();

        log.info("Found {} performance-metric templates for agency: {}", candidateTemplates.size(), agencyId);

        if (!candidateTemplates.isEmpty()) {
            List<Long> templateIds = candidateTemplates.stream()
                    .map(PMTemplate::getId)
                    .toList();
            log.debug("Found {} templates can be processed. Templates: {}", candidateTemplates.size(), templateIds);
            for (PMTemplate template : candidateTemplates) {
                try {
                    // Process templates. Should mark owner id as null (own by System)
                    PMResultSharedData resultSharedData = processTemplate(template, null);

                    notifyRunnerAboutResultCreated(template.getOwnerId(), resultSharedData, template.isMailReceive());
                } catch (PMTemplateProcessingException e) {
                    log.error("Process performance metric template, templateId=" + template.getId() + " failed!", e);
                } catch (Exception e) {
                    log.error("Unexpected errors while process template, templateId=" + template.getId(), e);
                }
            }
        }
    }

}
