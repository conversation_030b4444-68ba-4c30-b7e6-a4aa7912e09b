package com.siemens.spm.analysis.strategy.intercom;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.FormatStyle;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.siemens.spm.common.agency.utils.AgencyUtils;
import com.siemens.spm.common.util.DateTimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.siemens.spm.analysis.api.constant.TemplateConstant.ActionOnTemplate;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateGeneralVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.performancemetric.PMTemplateGeneralDataVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryReportInfoVO;
import com.siemens.spm.analysis.api.vo.summaryreport.SummaryTemplateDetailVO;
import com.siemens.spm.analysis.config.IntercomConfig;
import com.siemens.spm.analysis.domain.DetectorIntersectionResult;
import com.siemens.spm.analysis.domain.DetectorResult;
import com.siemens.spm.analysis.domain.PMMetadata;
import com.siemens.spm.analysis.domain.PMResult;
import com.siemens.spm.analysis.domain.PMResultSharedData;
import com.siemens.spm.analysis.util.MailConstants;
import com.siemens.spm.common.constant.CommonConstants;
import com.siemens.spm.common.constant.NotificationConstants;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.notification.NotificationType;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.NotificationMessageRequestVO;
import com.siemens.spm.common.shared.vo.TemplateDetailActionDataVO;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.NotificationUtil;
import com.siemens.spm.notification.config.MailConfig;
import com.siemens.spm.notification.exception.NotificationSenderException;
import com.siemens.spm.notification.service.NotificationSenderService;
import com.siemens.spm.notification.vo.EmailMessageVO;
import com.siemens.spm.spmstudiosdk.dto.StudioAgencyDto;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioAgencyService;
import com.siemens.spm.usermanagementservice.api.intercom.NotificationInterComController;
import com.siemens.spm.usermanagementservice.api.vo.DetectorReportManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.DetectorResultContentVO;
import com.siemens.spm.usermanagementservice.api.vo.PMManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.PMResultsContentVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryReportManagementContentVO;
import com.siemens.spm.usermanagementservice.api.vo.SummaryResultContentVO;
import com.siemens.spm.usermanagementservice.api.vo.request.NotificationsCreateRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

/**
 * <AUTHOR> Nguyen & Anh Mai
 */
@Slf4j
@Service
public class ReportNotificationStrategyBean implements ReportNotificationStrategy {

    private static final String SUMMARY_RESULT_CREATED_TEMPLATE = "summary_result_created";

    private static final String PM_RESULT_CREATED_TEMPLATE = "pm_result_created";

    private static final String DETECTOR_RESULT_CREATED_TEMPLATE = "detector_result_created";

    @Autowired
    private MailConfig mailConfig;

    @Autowired
    private IntercomConfig intercomConfig;

    @Autowired
    private SpringTemplateEngine templateEngine;

    @Autowired
    private NotificationSenderService notificationService;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private StudioAgencyService studioAgencyService;

    @Value("${spm.web-ui.summaryReportUrl}")
    private String summaryResultUrl;

    @Value("${spm.web-ui.pmResultUrl}")
    private String pmResultUrl;

    @Value("${spm.web-ui.detectorResultUrl}")
    private String detectorResultUrl;

    /**
     * {@inheritDoc}
     */
    @Override
    public void sendSummaryTemplateNotiAsync(SummaryTemplateDetailVO templateDetailVO,
                                             List<StudioUserDto> users,
                                             ActionOnTemplate action) {
        if (action == null) {
            throw new IllegalArgumentException("Action can not be null");
        }

        switch (action) {
        case CREATED -> sendSummaryTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.SUMMARY_TEMPLATE_CREATED,
                NotificationConstants.SUMMARY_TEMPLATE_CREATED_DESC);
        case ACTIVATE -> sendSummaryTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.SUMMARY_TEMPLATE_ACTIVATED,
                NotificationConstants.SUMMARY_TEMPLATE_ACTIVATED_DESC);
        case DEACTIVATE -> sendSummaryTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.SUMMARY_TEMPLATE_DEACTIVATED,
                NotificationConstants.SUMMARY_TEMPLATE_DEACTIVATED_DESC);
        case UPDATE -> sendSummaryTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.SUMMARY_TEMPLATE_UPDATED,
                NotificationConstants.SUMMARY_TEMPLATE_UPDATED_DESC);
        case DELETE -> sendSummaryTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.SUMMARY_TEMPLATE_DELETED,
                NotificationConstants.SUMMARY_TEMPLATE_DELETED_DESC);
        }
    }

    @Override
    public void sendPMTemplateNotiAsync(PMTemplateDetailVO templateDetailVO,
                                        List<StudioUserDto> users,
                                        ActionOnTemplate action) {
        if (action == null) {
            throw new IllegalArgumentException("Action can not be null");
        }

        switch (action) {
        case CREATED -> sendPMTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.PM_TEMPLATE_CREATED,
                NotificationConstants.PM_TEMPLATE_CREATED_DESC);
        case ACTIVATE -> sendPMTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.PM_TEMPLATE_ACTIVATED,
                NotificationConstants.PM_TEMPLATE_ACTIVATED_DESC);
        case DEACTIVATE -> sendPMTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.PM_TEMPLATE_DEACTIVATED,
                NotificationConstants.PM_TEMPLATE_DEACTIVATED_DESC);
        case UPDATE -> sendPMTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.PM_TEMPLATE_UPDATED,
                NotificationConstants.PM_TEMPLATE_UPDATED_DESC);
        case DELETE -> sendPMTemplateNoti(action, templateDetailVO, users,
                NotificationConstants.PM_TEMPLATE_DELETED,
                NotificationConstants.PM_TEMPLATE_DELETED_DESC);
        }
    }

    @Override
    public void sendDetectorTemplateNotiAsync(DetectorTemplateDetailVO templateDetailVO,
                                              List<StudioUserDto> userInternalVOList,
                                              ActionOnTemplate action) {
        if (action == null) {
            throw new IllegalArgumentException("Action can not be null");
        }

        switch (action) {
        case CREATED -> sendDetectorTemplateNoti(action, templateDetailVO, userInternalVOList,
                NotificationConstants.DETECTOR_TEMPLATE_CREATED,
                NotificationConstants.DETECTOR_TEMPLATE_CREATED_DESC);
        case ACTIVATE -> sendDetectorTemplateNoti(action, templateDetailVO, userInternalVOList,
                NotificationConstants.DETECTOR_TEMPLATE_ACTIVATED,
                NotificationConstants.DETECTOR_TEMPLATE_ACTIVATED_DESC);
        case DEACTIVATE -> sendDetectorTemplateNoti(action, templateDetailVO, userInternalVOList,
                NotificationConstants.DETECTOR_TEMPLATE_DEACTIVATED,
                NotificationConstants.DETECTOR_TEMPLATE_DEACTIVATED_DESC);
        case UPDATE -> sendDetectorTemplateNoti(action, templateDetailVO, userInternalVOList,
                NotificationConstants.DETECTOR_TEMPLATE_UPDATED,
                NotificationConstants.DETECTOR_TEMPLATE_UPDATED_DESC);
        case DELETE -> sendDetectorTemplateNoti(action, templateDetailVO, userInternalVOList,
                NotificationConstants.DETECTOR_TEMPLATE_DELETED,
                NotificationConstants.DETECTOR_TEMPLATE_DELETED_DESC);
        }
    }

    private void sendPMTemplateNoti(ActionOnTemplate action,
                                    PMTemplateDetailVO templateDetailVO,
                                    List<StudioUserDto> users,
                                    String notiNameKey,
                                    String notiDescriptionKey) {
        PMTemplateGeneralDataVO generalDataVO = templateDetailVO.getGeneralDataVO();

        ActionVO actionVO = null;
        if (action != null && action != ActionOnTemplate.DELETE) {
            Map<String, String> urlParams = new HashMap<>();
            urlParams.put("agency_id", String.valueOf(generalDataVO.getAgencyId()));
            urlParams.put("metric_type", generalDataVO.getMetricId());

            TemplateDetailActionDataVO actionDataVO = TemplateDetailActionDataVO.builder()
                    .editable(false)
                    .id(generalDataVO.getId())
                    .urlParams(urlParams)
                    .title("performance-metric.templates.show-form")
                    .build();

            actionVO = ActionVO.builder()
                    .data(actionDataVO)
                    .target(ActionTarget.PM_TEMPLATE_DETAIL)
                    .type(ActionType.OPEN_SIDE_PANEL)
                    .build();
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                notiNameKey, null, notiDescriptionKey, null);

        List<Integer> userIds = users.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(templateDetailVO);

        var requestVO = NotificationsCreateRequestVO.builder()
                .action(actionVO)
                .agencyId(generalDataVO.getAgencyId())
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.PERFORMANCE_METRIC_MANAGEMENT.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);
    }

    private void sendSummaryTemplateNoti(ActionOnTemplate action,
                                         SummaryTemplateDetailVO templateDetailVO,
                                         List<StudioUserDto> userVOs,
                                         String notiNameKey,
                                         String notiDescriptionKey) {
        ActionVO actionVO = null;
        if (action != null && action != ActionOnTemplate.DELETE) {
            Map<String, String> urlParams = new HashMap<>();
            urlParams.put("text", templateDetailVO.getName());
            urlParams.put("agency_id", String.valueOf(templateDetailVO.getAgencyId()));

            TemplateDetailActionDataVO templateDetailActionDataVO = TemplateDetailActionDataVO.builder()
                    .editable(false)
                    .id(templateDetailVO.getId())
                    .urlParams(urlParams)
                    .title("summary-report.templates.show-form")
                    .build();

            actionVO = ActionVO.builder()
                    .data(templateDetailActionDataVO)
                    .target(ActionTarget.REPORT_TEMPLATE_DETAIL)
                    .type(ActionType.OPEN_SIDE_PANEL)
                    .build();
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                notiNameKey, null, notiDescriptionKey, null);

        List<Integer> userIds = userVOs.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(templateDetailVO);

        var requestVO = NotificationsCreateRequestVO.builder()
                .action(actionVO)
                .agencyId(templateDetailVO.getAgencyId())
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.SUMMARY_REPORT_MANAGEMENT.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);
    }

    private void sendDetectorTemplateNoti(ActionOnTemplate action,
                                          DetectorTemplateDetailVO templateDetailVO,
                                          List<StudioUserDto> users,
                                          String notiNameKey,
                                          String notiDescriptionKey) {
        DetectorTemplateGeneralVO generalDataVO = templateDetailVO.getGeneralVO();

        ActionVO actionVO = null;
        if (action != null && action != ActionOnTemplate.DELETE) {
            Map<String, String> urlParams = new HashMap<>();
            urlParams.put("agency_id", AgencyUtils.getAgencyId());

            TemplateDetailActionDataVO actionDataVO = TemplateDetailActionDataVO.builder()
                    .editable(false)
                    .id(templateDetailVO.getId())
                    .urlParams(urlParams)
                    .title("detector-report.templates.show-form")
                    .build();

            actionVO = ActionVO.builder()
                    .data(actionDataVO)
                    .target(ActionTarget.DETECTOR_TEMPLATE_DETAIL)
                    .type(ActionType.OPEN_SIDE_PANEL)
                    .build();
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                notiNameKey, null, notiDescriptionKey, null);

        List<Integer> userIds = users.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(templateDetailVO);

        var requestVO = NotificationsCreateRequestVO.builder()
                .action(actionVO)
                .agencyId(Integer.valueOf(AgencyUtils.getAgencyId()))
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.DETECTOR_REPORT_MANAGEMENT.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);
    }

    private String createNotificationContent(PMTemplateDetailVO templateDetailVO) {
        if (templateDetailVO == null || templateDetailVO.getGeneralDataVO() == null) {
            return CommonConstants.JSON_EMPTY;
        }

        PMTemplateGeneralDataVO generalDataVO = templateDetailVO.getGeneralDataVO();
        PMManagementContentVO pmManagementContentVO = PMManagementContentVO.builder()
                .metricType(generalDataVO.getMetricId())
                .templateDesc(generalDataVO.getDescription())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(pmManagementContentVO);
        } catch (Exception e) {
            log.error("Error when create notification content for PM template", e);

            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    private String createNotificationContent(SummaryTemplateDetailVO templateDetailVO) {
        if (templateDetailVO == null) {
            return CommonConstants.JSON_EMPTY;
        }

        SummaryReportManagementContentVO summaryReportManagementContentVO = SummaryReportManagementContentVO.builder()
                .templateName(templateDetailVO.getName())
                .templateDescription(templateDetailVO.getDescription())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(summaryReportManagementContentVO);
        } catch (Exception e) {
            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    private String createNotificationContent(DetectorTemplateDetailVO templateDetailVO) {
        if (templateDetailVO == null) {
            return CommonConstants.JSON_EMPTY;
        }

        DetectorReportManagementContentVO detectorReportManagementContentVO = DetectorReportManagementContentVO.builder()
                .templateName(templateDetailVO.getGeneralVO().getName())
                .templateDescription(templateDetailVO.getGeneralVO().getDescription())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(detectorReportManagementContentVO);
        } catch (Exception e) {
            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    private void processCreateNotifications(NotificationsCreateRequestVO createRequestVO) {
        try {
            NotificationInterComController
                    .invokeCreateNotificationsForUsers(intercomConfig.getUserServiceEndpoint(), createRequestVO);
        } catch (Exception e) {
            log.warn("Something wrong when process create notifications!", e);
        }
    }

    @Override
    public void sendSummaryResultNotiAsync(SummaryReportInfoVO reportInfoVO,
                                           List<StudioUserDto> userVOs,
                                           ActionVO actionVO) {
        // Send notification
        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                NotificationConstants.SUMMARY_RESULT_CREATED, null, NotificationConstants.SUMMARY_RESULT_CREATED_DESC,
                null);

        List<Integer> userIds = userVOs.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(reportInfoVO);

        if (reportInfoVO == null) {
            log.error("ReportInfoVO is NULL");
            return;
        }
        Integer agencyId = reportInfoVO.getAgencyId();

        var requestVO = NotificationsCreateRequestVO.builder()
                .action(actionVO)
                .agencyId(agencyId)
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.SUMMARY_REPORT_NOTIFICATION.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);

        // Send email
        List<String> userEmails = userVOs.stream()
                .map(StudioUserDto::getEmail)
                .distinct()
                .toList();

        String dayOfWeeks = reportInfoVO.getWeekDays()
                .stream()
                .map(Object::toString)
                .collect(Collectors.joining(", "));

        Instant createdInstant = Instant.ofEpochMilli(reportInfoVO.getCreatedAt().getTime());
        ZoneId zoneId = ZoneId.of("UTC");
        try {
            zoneId = ZoneId.of(reportInfoVO.getTimeZone());
        } catch (Exception ex) {
            log.error("Unknown time zone: {} => Use UTC", reportInfoVO.getTimeZone());
        }
       
        ZonedDateTime createdDateTime = ZonedDateTime.ofInstant(createdInstant, zoneId);
        String createdAtString = DateTimeUtils.TIME_FORMAT_PATTERN_FOR_EMAIL.format(createdDateTime);

        Map<String, Object> variables = new HashMap<>();
        variables.put(MailConstants.USER, reportInfoVO.getOwnerVO());
        variables.put(MailConstants.REPORT_NAME, reportInfoVO.getName());
        variables.put(MailConstants.CREATED_AT, createdAtString);
        variables.put(MailConstants.AGENCY_NAME, reportInfoVO.getAgencyName());
        variables.put(MailConstants.FROM_DATE, reportInfoVO.getFromDate());
        variables.put(MailConstants.TO_DATE, reportInfoVO.getToDate());
        variables.put(MailConstants.FROM_TIME, reportInfoVO.getFromTime());
        variables.put(MailConstants.TO_TIME, reportInfoVO.getToTime());
        variables.put(MailConstants.AGGREGATION_UNIT, reportInfoVO.getAggregation());
        variables.put(MailConstants.DAY_OF_WEEKS, dayOfWeeks);
        variables.put(MailConstants.NUMBER_OF_INTERSECTIONS, reportInfoVO.getNumberOfIntersections());
        variables.put(MailConstants.SUMMARY_RESULT_URL, summaryResultUrl + reportInfoVO.getId());

        for (String userEmail : userEmails) {
            sendMail(SUMMARY_RESULT_CREATED_TEMPLATE, mailConfig.getSenderEmail(), userEmail, variables,
                    NotificationConstants.SUMMARY_RESULT_CREATED);
        }
    }

    @Override
    public void sendPMResultNotiAsync(PMResultSharedData resultSharedData,
                                      List<StudioUserDto> users,
                                      boolean mailReceive) {
        if (resultSharedData == null || users == null) {
            throw new IllegalArgumentException();
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                NotificationConstants.PM_RESULT_CREATED, null, NotificationConstants.PM_RESULT_CREATED_DESC,
                new Integer[] { resultSharedData.getResults().size() });

        List<Integer> userIds = users.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(resultSharedData);

        NotificationsCreateRequestVO requestVO = NotificationsCreateRequestVO.builder()
                .agencyId(resultSharedData.getAgencyId())
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.PERFORMANCE_METRIC_NOTIFICATION.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);

        if (mailReceive) {
            sendPMResultMailNoti(resultSharedData, users);
        }
    }

    private void sendPMResultMailNoti(PMResultSharedData resultSharedData, List<StudioUserDto> users) {
        PMMetadata metadata = resultSharedData.getMetadata();

        Integer binSize = null;
        if (metadata.getBinSize() != null) {

            binSize = metadata.getBinSize() / 60;
        }

        String agencyName = CommonConstants.NOT_AVAILABLE;
        try {
            agencyName = studioAgencyService.getAgencyById(resultSharedData.getAgencyId())
                    .map(StudioAgencyDto::getAgencyName)
                    .orElse(CommonConstants.NOT_AVAILABLE);
        } catch (StudioException e) {
            log.error("Error when get agency by id from Studio", e);
        }

        String description = "";
        if (resultSharedData.getDescription() != null) {
            description = resultSharedData.getDescription();
        }

        Timestamp maxCreatedTimestamp = resultSharedData.getResults()
            .stream()
            .map(PMResult::getCreatedAt)
            .max(Timestamp::compareTo)
            .orElse(null);

        Instant createdInstant = Instant.ofEpochMilli(maxCreatedTimestamp.getTime());
        ZoneId zoneId = ZoneId.of("UTC");
        try {
            zoneId = ZoneId.of(resultSharedData.getTimezone());
        } catch (Exception ex) {
            log.error("Unknown time zone: {} => Use UTC", resultSharedData.getTimezone());
        }
       
        ZonedDateTime createdDateTime = ZonedDateTime.ofInstant(createdInstant, zoneId);
        String createdAtString = DateTimeUtils.TIME_FORMAT_PATTERN_FOR_EMAIL.format(createdDateTime);

        String dayOfWeeks = resultSharedData.getWeekDays()
                .stream()
                .sorted()
                .map(Object::toString)
                .collect(Collectors.joining(", "));

        Map<String, Object> variables = new HashMap<>();
        variables.put(MailConstants.AGENCY_NAME, agencyName);
        variables.put(MailConstants.PM_TYPE, resultSharedData.getMetricType().getTranslatedName());
        variables.put(MailConstants.DESCRIPTION, description);
        variables.put(MailConstants.CREATED_AT, createdAtString);
        variables.put(MailConstants.BIN_SIZE, binSize);
        variables.put(MailConstants.FROM_TIME, metadata.getFromTime());
        variables.put(MailConstants.TO_TIME, metadata.getToTime());
        variables.put(MailConstants.FROM_DATE, resultSharedData.getFromDate());
        variables.put(MailConstants.TO_DATE, resultSharedData.getToDate());
        variables.put(MailConstants.DAY_OF_WEEKS, dayOfWeeks);
        variables.put(MailConstants.NUM_RESULTS, resultSharedData.getResults().size());
        variables.put(MailConstants.PM_RESULTS, resultSharedData.getResults());
        variables.put(MailConstants.PM_RESULT_URL, pmResultUrl);

        // @TOAN???: add agency_id as query param => do we still support agency_id as rest param?
        // String formattedAgencyIdQuery = String.format("?agency_id=%d", resultSharedData.getAgencyId());
        // variables.put(MailConstants.AGENCY_ID_QUERY, formattedAgencyIdQuery);

        for (StudioUserDto user : users) {
            variables.put(MailConstants.USER, user);
            sendMail(PM_RESULT_CREATED_TEMPLATE, mailConfig.getSenderEmail(), user.getEmail(), variables,
                    NotificationConstants.PM_RESULT_CREATED);
        }
    }

    private void sendDetectorResultMailNoti(DetectorResult detectorResult, List<StudioUserDto> users) {

        String agencyName = CommonConstants.NOT_AVAILABLE;
        try {
            agencyName = studioAgencyService.getAgencyById(detectorResult.getAgencyId())
                    .map(StudioAgencyDto::getAgencyName)
                    .orElse(CommonConstants.NOT_AVAILABLE);
        } catch (StudioException e) {
            log.error("Error when get agency by id from Studio", e);
        }

        String description = "";
        if (detectorResult.getDescription() != null) {
            description = detectorResult.getDescription();
        }

        String dayOfWeeks = detectorResult.getWeekDays()
                .stream()
                .sorted((day1, day2) -> {
                    // Map days to their order (Monday = 1, Sunday = 7)
                    int order1 = switch (day1.toString().toUpperCase()) {
                        case "MONDAY" -> 1;
                        case "TUESDAY" -> 2;
                        case "WEDNESDAY" -> 3;
                        case "THURSDAY" -> 4;
                        case "FRIDAY" -> 5;
                        case "SATURDAY" -> 6;
                        case "SUNDAY" -> 7;
                        default -> 8; // Handle any unexpected values
                    };
                    int order2 = switch (day2.toString().toUpperCase()) {
                        case "MONDAY" -> 1;
                        case "TUESDAY" -> 2;
                        case "WEDNESDAY" -> 3;
                        case "THURSDAY" -> 4;
                        case "FRIDAY" -> 5;
                        case "SATURDAY" -> 6;
                        case "SUNDAY" -> 7;
                        default -> 8; // Handle any unexpected values
                    };
                    return Integer.compare(order1, order2);
                })
                .map(Object::toString)
                .collect(Collectors.joining(", "));
        //prepare convert Java.util.sql.TimeStamp to String format Ex:
        var timeConvert = detectorResult.getCreatedAt().toLocalDateTime();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("EEEE, MMMM dd, yyyy 'at' hh:mm:ss a");
        String createdAtFormatted = timeConvert.format(formatter) + " " + detectorResult.getTimezone();

        Map<String, Object> variables = new HashMap<>();
        variables.put(MailConstants.AGENCY_NAME, agencyName);
        variables.put(MailConstants.DESCRIPTION, description);
        variables.put(MailConstants.CREATED_AT,createdAtFormatted);
        variables.put(MailConstants.FROM_TIME, detectorResult.getFromTime());
        variables.put(MailConstants.TO_TIME, detectorResult.getToTime());
        variables.put(MailConstants.FROM_DATE, detectorResult.getFromDate());
        variables.put(MailConstants.TO_DATE, detectorResult.getToDate());
        variables.put(MailConstants.DAY_OF_WEEKS, dayOfWeeks);
        variables.put(MailConstants.NUM_RESULTS, detectorResult.getIntersectionResults().size());
        variables.put(MailConstants.DETECTOR_ID, detectorResult.getId());
        variables.put(MailConstants.DETECTOR_RESULT_URL, detectorResultUrl);
        //add agency_id as query param
        String formattedAgencyIdQuery = String.format("?agency_id=%d", detectorResult.getAgencyId());
        variables.put(MailConstants.AGENCY_ID_QUERY, formattedAgencyIdQuery);

        for (StudioUserDto user : users) {
            variables.put(MailConstants.USER, user);
            sendMail(DETECTOR_RESULT_CREATED_TEMPLATE, mailConfig.getSenderEmail(), user.getEmail(), variables,
                    NotificationConstants.DETECTOR_RESULT_CREATED);
        }
    }

    @Override
    public void sendDetectorResultNotiAsync(DetectorResult detectorResult,
                                            List<StudioUserDto> users,
                                            boolean mailReceive) {
        if (detectorResult == null || users == null) {
            throw new IllegalArgumentException();
        }

        List<NotificationMessageRequestVO> notificationMessageVOs = NotificationUtil.createNotificationMessage(
                NotificationConstants.DETECTOR_RESULT_CREATED, null, NotificationConstants.DETECTOR_RESULT_CREATED_DESC,
                new Integer[] { detectorResult.getIntersectionResults().size() });

        List<Integer> userIds = users.stream()
                .map(StudioUserDto::getId)
                .distinct()
                .toList();

        String notiContent = createNotificationContent(detectorResult);

        NotificationsCreateRequestVO requestVO = NotificationsCreateRequestVO.builder()
                .agencyId(detectorResult.getAgencyId())
                .content(notiContent)
                .notificationMessages(notificationMessageVOs)
                .typeId(NotificationType.DETECTOR_REPORT_NOTIFICATION.getId())
                .userIds(userIds)
                .build();

        processCreateNotifications(requestVO);

        if (mailReceive) {
            sendDetectorResultMailNoti(detectorResult, users);
        }
    }

    private String createNotificationContent(SummaryReportInfoVO reportInfoVO) {
        if (reportInfoVO == null) {
            return CommonConstants.JSON_EMPTY;
        }

        SummaryResultContentVO summaryResultContentVO = SummaryResultContentVO.builder()
                .reportName(reportInfoVO.getName())
                .fromTime(reportInfoVO.getFromTime())
                .toTime(reportInfoVO.getToTime())
                .fromDate(reportInfoVO.getFromDate())
                .toDate(reportInfoVO.getToDate())
                .timeZone(reportInfoVO.getTimeZone())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(summaryResultContentVO);
        } catch (Exception e) {
            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    private String createNotificationContent(PMResultSharedData resultSharedData) {
        if (resultSharedData == null || resultSharedData.getResults() == null
                || resultSharedData.getMetadata() == null) {
            throw new IllegalArgumentException();
        }

        Set<PMResult> results = resultSharedData.getResults();
        Map<Long, String> idAndIntNameMap = results.stream()
                .collect(Collectors.toMap(PMResult::getId, PMResult::getIntName));

        PMMetadata metadata = resultSharedData.getMetadata();

        Set<DayOfWeek> weekDays = Set.of();
        if (resultSharedData.getWeekDays() != null) {
            weekDays = new TreeSet<>(resultSharedData.getWeekDays());              
        }
        PMResultsContentVO pmResultsContentVO = PMResultsContentVO.builder()
                .results(idAndIntNameMap)
                .metricType(resultSharedData.getMetricType())
                .fromTime(metadata.getFromTime())
                .toTime(metadata.getToTime())
                .fromDate(resultSharedData.getFromDate())
                .toDate(resultSharedData.getToDate())
                .weekDays(weekDays)
                .timeZone(resultSharedData.getTimezone())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(pmResultsContentVO);
        } catch (Exception e) {
            log.error("Error when create notification content", e);

            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    private String createNotificationContent(DetectorResult detectorResult) {
        if (detectorResult == null || detectorResult.getIntersectionResults() == null) {
            throw new IllegalArgumentException();
        }

        Set<DetectorIntersectionResult> results = detectorResult.getIntersectionResults();
        Map<String, String> idAndIntNameMap = results.stream()
                .collect(Collectors.toMap(DetectorIntersectionResult::getIntId, DetectorIntersectionResult::getIntName));

        Set<DayOfWeek> weekDays = Set.of();
        if (detectorResult.getWeekDays() != null) {
            weekDays = new TreeSet<>(detectorResult.getWeekDays());              
        }
        DetectorResultContentVO detectorResultsContentVO = DetectorResultContentVO.builder()
                .results(idAndIntNameMap)
                .fromTime(detectorResult.getFromTime())
                .toTime(detectorResult.getToTime())
                .fromDate(detectorResult.getFromDate())
                .toDate(detectorResult.getToDate())
                .weekDays(weekDays)
                .timeZone(detectorResult.getTimezone())
                .build();

        String notiContent;

        try {
            notiContent = BeanFinder.getDefaultObjectMapper().writeValueAsString(detectorResultsContentVO);
        } catch (Exception e) {
            log.error("Error when create notification content", e);

            notiContent = CommonConstants.JSON_EMPTY;
        }

        return notiContent;
    }

    /**
     * @param templateName Template file in resources/templates/mail
     * @param from         Sender email
     * @param to           Receiver email
     * @param variables
     * @param subjectKey
     */
    private void sendMail(String templateName,
                          String from,
                          String to,
                          Map<String, Object> variables,
                          String subjectKey) {
        Locale locale = Locale.getDefault();
        Context context = new Context(locale);
        context.setVariables(variables);

        String content = templateEngine.process(templateName, context);
        String subject = messageSource.getMessage(subjectKey, null, locale);

        EmailMessageVO emailVO = EmailMessageVO.builder()
                .sender(from)
                .toRecipients(List.of(to))
                .subject(subject)
                .content(content)
                .build();

        try {
            notificationService.sendMessage(emailVO);
        } catch (NotificationSenderException e) {
            log.error("Error when send mail", e);
        }
    }
}
