package com.siemens.spm.analysis.strategy.intercom;

import com.siemens.spm.analysis.api.vo.request.IntersectionIdListRequestVO;
import com.siemens.spm.analysis.repository.ConfigEventRepository;
import com.siemens.spm.analysis.repository.DetectorIntersectionResultRepository;
import com.siemens.spm.analysis.repository.IntersectionConfigRepository;
import com.siemens.spm.analysis.repository.IntersectionMetadataRepository;
import com.siemens.spm.analysis.repository.IntersectionTopologyRepository;
import com.siemens.spm.analysis.repository.OptimizationHistoryRepository;
import com.siemens.spm.analysis.repository.PMResultRepository;
import com.siemens.spm.analysis.repository.PerfLogChunkRepository;
import com.siemens.spm.analysis.repository.PerfLogGapRepository;
import com.siemens.spm.analysis.repository.PhaseEventRepository;
import com.siemens.spm.analysis.repository.PhaseStatRepository;
import com.siemens.spm.analysis.repository.ReportResultStatisticRepository;
import com.siemens.spm.analysis.repository.TaskProgressRepository;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.shared.domaintype.IntersectionScope;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.vo.IntersectionSearchResponseDataVO;
import com.siemens.spm.common.shared.vo.IntersectionSimpleDataVO;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.datahub.api.boundary.DataIntegrationService;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionSearchRequestVO;
import com.siemens.spm.datahub.api.vo.DataHubIntersectionVO;
import com.siemens.spm.datahub.api.vo.response.DataHubIntersectionResponseVO;
import com.siemens.spm.datahub.exception.DataHubException;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> Nguyen
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IntersectionStrategyBean implements IntersectionStrategy {

    private final IntersectionConfigRepository intersectionConfigRepository;

    private final IntersectionMetadataRepository intersectionMetadataRepository;

    private final IntersectionTopologyRepository intersectionTopologyRepository;

    private final TaskProgressRepository taskProgressRepository;

    private final PMResultRepository pmResultRepository;

    private final ReportResultStatisticRepository reportResultStatisticRepository;

    private final PhaseStatRepository phaseStatRepository;

    private final PhaseEventRepository phaseEventRepository;

    private final PerfLogChunkRepository perfLogChunkRepository;

    private final PerfLogGapRepository perfLogGapRepository;

    private final OptimizationHistoryRepository optimizationHistoryRepository;

    private final ConfigEventRepository configEventRepository;

    private final DetectorIntersectionResultRepository intersectionResultRepository;

    private final DataIntegrationService dataIntegrationService;

    @Override
    public IntersectionSearchResponseDataVO searchIntersectionsInternal(IntersectionSearchRequestVO requestVO) {
        IntersectionInternalListVO intersectionListVO = doSearch(requestVO);
        List<IntersectionSimpleDataVO> intersectionSimpleDataVOS = intersectionListVO.getIntersections()
                .stream()
                .map(intersectionVO -> IntersectionSimpleDataVO.builder()
                        .id(intersectionVO.getId())
                        .name(intersectionVO.getName())
                        .status(IntersectionStatus.toInsight(intersectionVO.getStatus()))
                        .build())
                .toList();

        return IntersectionSearchResponseDataVO.builder()
                .intersections(intersectionSimpleDataVOS)
                .totalCount(intersectionListVO.getTotalCount())
                .scope(IntersectionScope.SPECIFIC_INTERSECTIONS)
                .build();
    }

    @Override
    public IntersectionSearchResponseDataVO searchIntersectionsInternal(String intIdsString,
                                                                        IntersectionSearchRequestVO searchRequestVO) {
        if (intIdsString == null || searchRequestVO == null) {
            throw new IllegalArgumentException();
        }

        if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intIdsString)) {
            String[] intIdsArr = intIdsString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
            searchRequestVO.setIntersectionIds(Arrays.asList(intIdsArr));
        }

        IntersectionInternalListVO intersectionListVO = doSearch(searchRequestVO);
        List<IntersectionSimpleDataVO> intersectionSimpleDataVOS = intersectionListVO.getIntersections()
                .stream()
                .map(intersectionVO -> IntersectionSimpleDataVO.builder()
                        .id(intersectionVO.getId())
                        .name(intersectionVO.getName())
                        .status(IntersectionStatus.toInsight(intersectionVO.getStatus()))
                        .build())
                .toList();

        IntersectionSearchResponseDataVO responseDataVO = IntersectionSearchResponseDataVO.builder()
                .intersections(intersectionSimpleDataVOS)
                .totalCount(intersectionListVO.getTotalCount())
                .build();

        if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intIdsString)) {
            responseDataVO.setScope(IntersectionScope.ALL_INTERSECTIONS);
        } else {
            responseDataVO.setScope(IntersectionScope.SPECIFIC_INTERSECTIONS);
        }

        return responseDataVO;
    }

    @Override
    public IntersectionInternalListVO getIntersectionsByFilterInternal(IntersectionSearchRequestVO searchRequest) {
        return doSearch(searchRequest);
    }

    @Override
    @Transactional
    public SimpleResultObject deleteIntersectionByIds(IntersectionIdListRequestVO request) {
        try {
            List<String> ids = request.getIds();
            log.info("Deleting Ids = " + ids);
            intersectionConfigRepository.deleteAllByIntUUIDIn(ids);
            intersectionMetadataRepository.deleteAllByIdInBatch(ids);
            intersectionTopologyRepository.deleteAllByIntUUIDIn(ids);

            pmResultRepository.deleteAllByIntIdIn(ids);
            intersectionResultRepository.deleteAllByIntIdIn(ids);
            taskProgressRepository.deleteAllByIntUUIDIn(ids);
            reportResultStatisticRepository.deleteAllByIntUUIDIn(ids);
            phaseEventRepository.deleteAllByIntUUIDIn(ids);
            phaseStatRepository.deleteAllByIntUUIDIn(ids);
            perfLogChunkRepository.deleteAllByIntUUIDIn(ids);
            perfLogGapRepository.deleteAllByIntUUIDIn(ids);
            optimizationHistoryRepository.deleteAllByIntUUIDIn(ids);
            configEventRepository.deleteAllByIntUUIDIn(ids);

            return new SimpleResultObject(SimpleResultObject.SimpleStatusCode.SUCCESS);
        } catch (Exception e) {
            log.error("Error deleting intersection by ids", e);
            return new SimpleResultObject(SimpleResultObject.SimpleStatusCode.ERROR);
        }
    }

    private IntersectionInternalListVO doSearch(IntersectionSearchRequestVO requestVO) {
        Pair<List<DataHubIntersectionVO>, Long> response = getIntersectionInternalListVO(requestVO);
        List<DataHubIntersectionVO> intersectionInternalListVO = response.getFirst();

        IntersectionInternalListVO resultIntersectionListVO = IntersectionInternalListVO.builder()
                .totalCount(0L)
                .agencyId(requestVO.getAgencyId())
                .intersections(new ArrayList<>())
                .build();

        if (CollectionUtils.isNotEmpty(intersectionInternalListVO)) {
            List<IntersectionInternalVO> convertedList = intersectionInternalListVO.stream()
                    .map(this::convertToIntersectionInternalVO)
                    .toList();

            resultIntersectionListVO = IntersectionInternalListVO.builder()
                    .totalCount(response.getSecond())
                    .agencyId(requestVO.getAgencyId())
                    .intersections(convertedList)
                    .build();
        }

        return resultIntersectionListVO;
    }

    private IntersectionInternalVO convertToIntersectionInternalVO(DataHubIntersectionVO dataHubIntersectionVO) {
        IntersectionInternalVO intersectionInternalVO = new IntersectionInternalVO();
        intersectionInternalVO.setId(dataHubIntersectionVO.getId());
        intersectionInternalVO.setName(dataHubIntersectionVO.getName());
        intersectionInternalVO.setStatus(IntersectionStatus.toInsight(dataHubIntersectionVO.getStatus()));
        intersectionInternalVO.setTimezone(dataHubIntersectionVO.getTimezone());
        return intersectionInternalVO;
    }

    private Pair<List<DataHubIntersectionVO>, Long> getIntersectionInternalListVO(IntersectionSearchRequestVO requestVO) {
        try {
            DataHubIntersectionSearchRequestVO request = DataHubIntersectionSearchRequestVO.builder()
                    .agencyId(requestVO.getAgencyId())
                    .exclusionaryIds(requestVO.getExclusionaryIds())
                    .intersectionIds(requestVO.getIntersectionIds())
                    .orderByColumns(requestVO.getOrderByColumns())
                    .shouldPaginate(requestVO.getShouldPaginate())
                    .status(StringUtils.isNotEmpty(requestVO.getStatus()) ?
                            IntersectionStatus.toDataHub(requestVO.getStatus()) :
                            null)
                    .text(requestVO.getText())
                    .bottomRight(requestVO.getBottomRight())
                    .topLeft(requestVO.getTopLeft())
                    .page(requestVO.getPage())
                    .size(requestVO.getSize())
                    .build();
            DataHubIntersectionResponseVO response = dataIntegrationService.getIntersectionsByFilter(request);
            return Optional.ofNullable(response)
                    .map(r -> Pair.of(r.getItems(), r.getTotalItems()))
                    .orElse(Pair.of(Collections.emptyList(), 0L));
        } catch (DataHubException e) {
            log.error("Failed to retrieve intersection data from user service", e);
            throw new RuntimeException("Error retrieving intersection data", e);
        }
    }
}
