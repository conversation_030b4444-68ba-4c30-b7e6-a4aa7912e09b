package com.siemens.spm.analysis.domain;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorScope;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.util.BeanFinder;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PostLoad;
import jakarta.persistence.PostUpdate;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.DayOfWeek;
import java.time.LocalTime;
import java.util.List;
import java.util.Set;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = DetectorTemplate.TABLE_NAME, schema = AGENCY_SCHEMA_PLACEHOLDER)
@Slf4j
public class DetectorTemplate extends AuditableEntity {

    public static final String TABLE_NAME = "detector_template";

    public static final class ColumnName {
        private ColumnName() {}

        public static final String ID = "id";

        public static final String NAME = "name";
        public static final String DESCRIPTION = "description";
        public static final String AGENCY_ID = "agency_id";
        public static final String OWNER_ID = "owner_id";
        public static final String DETECTOR_SCOPE = "detector_scope";
        public static final String INTERSECTION_LIST = "intersection_list";
        public static final String TIME_ZONE = "time_zone";
        public static final String TIME_ZONE_ID = "timezone_id";
        public static final String START_TIME = "start_time";
        public static final String END_TIME = "end_time";
        public static final String AGGREGATION = "aggregation";
        public static final String WEEK_DAYS = "week_days";
        public static final String DATE_RANGE = "date_range";
        public static final String STATUS = "status";
        public static final String DELETED = "deleted";
        public static final String PHASES = "phases";
        public static final String DETECTORS = "detectors";
        public static final String METRICS = "metrics";
        public static final String MAIL_RECEIVED = "mail_received";
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @NotNull
    @Column(name = ColumnName.NAME, length = TemplateConstant.REPORT_TEMPLATE_NAME_MAX_LENGTH)
    private String name;

    @Column(name = ColumnName.DESCRIPTION, length = TemplateConstant.REPORT_TEMPLATE_DESCRIPTION_MAX_LENGTH)
    private String description;

    @NotNull
    @Column(name = ColumnName.AGENCY_ID)
    private Integer agencyId;

    @NotNull
    @Column(name = ColumnName.OWNER_ID)
    private Long ownerId;

    @NotNull
    @Column(name = ColumnName.TIME_ZONE)
    private String timezone;

    @NotNull
    @Column(name = ColumnName.TIME_ZONE_ID)
    private String timezoneId;

    @NotNull
    @Column(name = ColumnName.START_TIME)
    private LocalTime startTime;

    @NotNull
    @Column(name = ColumnName.END_TIME)
    private LocalTime endTime;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = ColumnName.AGGREGATION)
    private TemplateAggregation aggregation;

    @NotNull
    @Column(name = ColumnName.WEEK_DAYS)
    private String weekDaysJson;

    @NotNull
    @Column(name = ColumnName.DATE_RANGE)
    private String dateRangeJson;

    @NotNull
    @Column(name = ColumnName.METRICS, columnDefinition = "TEXT")
    private String metricsJson;

    @NotNull
    @Column(name = ColumnName.INTERSECTION_LIST, columnDefinition = "TEXT")
    private String intersectionList;

    @NotNull
    @Column(name = ColumnName.DETECTOR_SCOPE)
    @Enumerated(EnumType.STRING)
    private DetectorScope detectorScope;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = ColumnName.STATUS)
    private TemplateStatus status;

    @NotNull
    @Column(name = ColumnName.DELETED)
    private boolean deleted;

    @Column(name = ColumnName.MAIL_RECEIVED)
    private boolean mailReceived;

    @NotNull
    @Column(name = ColumnName.PHASES)
    private String phasesJson;

    @Column(name = ColumnName.DETECTORS)
    private String detectorsJson;

    @OneToMany(mappedBy = "template")
    private Set<DetectorResult> results;

    @Transient
    private Set<DayOfWeek> weekDays;

    @Transient
    private DateRange dateRange;

    @Transient
    private List<DetectorMetric> metric;

    @Transient
    private List<Integer> phases;

    @Transient
    private List<Integer> detectors;


    @OneToOne(optional = false, cascade = CascadeType.REMOVE)
    @JoinColumn(name = "schedule_id", referencedColumnName = TemplateSchedule.ColumnName.ID)
    private TemplateSchedule schedule;

    public void persistTransientFields() throws JsonProcessingException {
        if (weekDays == null || dateRange == null || metric == null || phases == null) {
            log.error("DetectorTemplate is missing data before persist to DB");
            throw new IllegalArgumentException("DetectorTemplate is missing data before persist to DB");
        }

        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();
        weekDaysJson = objectMapper.writeValueAsString(weekDays);
        dateRangeJson = objectMapper.writeValueAsString(dateRange);
        metricsJson = objectMapper.writeValueAsString(metric);
        phasesJson = objectMapper.writeValueAsString(phases);
        if (detectors != null) {
            detectorsJson = objectMapper.writeValueAsString(detectors);
        }
    }

    @PostLoad
    @PostUpdate
    private void postLoadOrUpdate() {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();
        try {
            metric = objectMapper.readValue(metricsJson, new TypeReference<>() {});
            dateRange = objectMapper.readValue(dateRangeJson, DateRange.class);
            weekDays = objectMapper.readValue(weekDaysJson, new TypeReference<>() {});
            phases = objectMapper.readValue(phasesJson, new TypeReference<>() {});
            if (detectorsJson != null) {
                detectors = objectMapper.readValue(detectorsJson, new TypeReference<>() {});
            }
        } catch (JsonProcessingException e) {
            log.error("Error when deserialize metricJson, dateRangeJson, weekDaysJson", e);

            // This should be a bug
            metric = null;
            dateRange = null;
            weekDays = null;
            phases = null;
            detectors = null;
        }
    }
}
