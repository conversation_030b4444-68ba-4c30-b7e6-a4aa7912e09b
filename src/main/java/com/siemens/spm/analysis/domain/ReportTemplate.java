package com.siemens.spm.analysis.domain;

import java.time.LocalTime;
import java.util.Set;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;

import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.enums.TemplateAggregation;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.common.audit.AuditableEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

/**
 * <AUTHOR> Nguyen
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = ReportTemplate.TABLE_NAME, schema = AGENCY_SCHEMA_PLACEHOLDER)
public class ReportTemplate extends AuditableEntity {

    public static final String TABLE_NAME = "report_template";

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String DESCRIPTION = "description";
        public static final String AGENCY_ID = "agency_id";
        public static final String OWNER_ID = "owner_id";
        public static final String INTERSECTION_LIST = "intersection_list";
        public static final String TIME_ZONE = "time_zone";
        public static final String TIME_ZONE_ID = "timezone_id";
        public static final String START_TIME = "start_time";
        public static final String END_TIME = "end_time";
        public static final String AGGREGATION = "aggregation";
        public static final String WEEK_DAYS = "week_days";
        public static final String DATE_RANGE = "date_range";
        public static final String STATUS = "status";
        public static final String DELETED = "deleted";
        public static final String SCHEDULE_ID = "schedule_id";
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @NotNull
    @Column(name = ColumnName.NAME, length = TemplateConstant.REPORT_TEMPLATE_NAME_MAX_LENGTH)
    private String name;

    @Column(name = ColumnName.DESCRIPTION, length = TemplateConstant.REPORT_TEMPLATE_DESCRIPTION_MAX_LENGTH)
    private String description;

    @NotNull
    @Column(name = ColumnName.AGENCY_ID)
    private Integer agencyId;

    @NotNull
    @Column(name = ColumnName.OWNER_ID)
    private Long ownerId;

    @NotNull
    @Column(name = ColumnName.TIME_ZONE)
    private String timezone;

    @NotNull
    @Column(name = ColumnName.TIME_ZONE_ID)
    private String timezoneId;

    @NotNull
    @Column(name = ColumnName.START_TIME)
    private LocalTime startTime;

    @NotNull
    @Column(name = ColumnName.END_TIME)
    private LocalTime endTime;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = ColumnName.AGGREGATION)
    private TemplateAggregation aggregation;

    @NotNull
    @Column(name = ColumnName.WEEK_DAYS)
    private String weekDays;

    @NotNull
    @Column(name = ColumnName.DATE_RANGE)
    private String dateRange;

    @NotNull
    @Column(name = ColumnName.INTERSECTION_LIST, columnDefinition = "TEXT")
    private String intersectionList;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = ColumnName.STATUS)
    private TemplateStatus status;

    @NotNull
    @Column(name = ColumnName.DELETED)
    private boolean deleted;

    @OneToOne(cascade = CascadeType.REMOVE)
    @JoinColumn(name = ColumnName.SCHEDULE_ID, referencedColumnName = TemplateSchedule.ColumnName.ID)
    private TemplateSchedule schedule;

    @OneToMany(mappedBy = "reportTemplate", cascade = CascadeType.REMOVE)
    private Set<ReportResult> results;

}
