package com.siemens.spm.analysis.domain;

import java.time.DayOfWeek;
import java.util.Set;

import com.siemens.spm.common.constant.AgencyConstants;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PostLoad;
import jakarta.persistence.PostUpdate;
import jakarta.persistence.PrePersist;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.common.audit.AuditableEntity;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.util.BeanFinder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import static com.siemens.spm.common.constant.AgencyConstants.AGENCY_SCHEMA_PLACEHOLDER;

@Slf4j
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "pm_template", schema = AGENCY_SCHEMA_PLACEHOLDER)
public class PMTemplate extends AuditableEntity {

    public static final class ColumnName {

        private ColumnName() {
        }

        public static final String ID = "id";

    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ColumnName.ID)
    private Long id;

    @Column(name = "description")
    private String description;

    @NotNull
    @Column(name = "agency_id")
    private Integer agencyId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "metric_type")
    private AnalysisType metricType;

    @NotNull
    @Column(name = "timezone")
    private String timezone;

    @NotNull
    @Column(name = "timezone_id")
    private String timezoneId;

    /**
     * Json for metadata fields(like time of analysis, binSize, ...)
     * <p>
     * This field do not update outside, only retrieve and update through {@link PMMetadata} object
     */
    @NotNull
    @Column(name = "metadata_json", columnDefinition = "TEXT")
    private String metadataJson;

    @NotNull
    @Column(name = "week_days")
    private String weekDaysJson;

    @NotNull
    @Column(name = "date_range")
    private String dateRangeJson;

    /**
     * Json lob which combine list of intersections
     */
    @NotNull
    @Column(name = "intersection_list", columnDefinition = "TEXT")
    private String intersectionList;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private TemplateStatus status;

    /**
     * Soft delete used for dynamic action redirect in the notification
     */
    @Column(name = "deleted")
    private boolean deleted;

    @NotNull
    @Column(name = "owner_id")
    private Long ownerId;

    @Column(name = "mail_receive")
    private Boolean mailReceive;

    @OneToOne(optional = false, cascade = CascadeType.REMOVE)
    @JoinColumn(name = "schedule_id", referencedColumnName = TemplateSchedule.ColumnName.ID)
    private TemplateSchedule schedule;

    @OneToMany(mappedBy = "template", cascade = CascadeType.REMOVE)
    private Set<PMResult> results;

    @Transient
    private PMMetadata metadata;

    @Transient
    private DateRange dateRange;

    @Transient
    private Set<DayOfWeek> weekDays;

    @PrePersist
    private void preSave() throws JsonProcessingException {
        if (metadata == null || dateRange == null || weekDays == null) {
            throw new IllegalArgumentException("PMTemplate is missing data before persist to DB");
        }

        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();

        metadataJson = objectMapper.writeValueAsString(metadata);
        dateRangeJson = objectMapper.writeValueAsString(dateRange);
        weekDaysJson = objectMapper.writeValueAsString(weekDays);
    }

    @PostLoad
    @PostUpdate
    public void postLoadOrUpdate() {
        ObjectMapper objectMapper = BeanFinder.getDefaultObjectMapper();
        try {
            metadata = objectMapper.readValue(metadataJson, PMMetadata.class);
            dateRange = objectMapper.readValue(dateRangeJson, DateRange.class);
            weekDays = objectMapper.readValue(weekDaysJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            log.error("Error when deserialize metadataJson, dateRangeJson, weekDaysJson", e);

            // This should be a bug
            metadata = null;
            dateRange = null;
            weekDays = null;
        }
    }

    /**
     * Convenient way to check user want to receive mail notification or not?
     *
     * @return {@code true} if user want to receive mail notification, other wise return {@code false}
     */
    public boolean isMailReceive() {
        return mailReceive != null && mailReceive;
    }

}
