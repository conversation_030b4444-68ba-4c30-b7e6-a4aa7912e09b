package com.siemens.spm.analysis.detectorreport.template;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.siemens.spm.analysis.detectorreport.template.aggregation.AggregationStrategy;
import com.siemens.spm.analysis.detectorreport.template.aggregation.AggregationStrategyFactory;
import com.siemens.spm.analysis.detectorreport.template.metrics.MetricsCalculator;
import com.siemens.spm.analysis.domain.DetectorIntersectionResult;
import com.siemens.spm.analysis.domain.DetectorMetric;
import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.util.DateUtils;
import com.siemens.spm.analysis.vo.detectorreport.DetectorDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorDetailDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorDetailPhaseVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorPhaseDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportDetailChartVO;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class DetectorCalculatorBean implements DetectorCalculator {

    private final AggregationStrategyFactory aggregationStrategyFactory;

    private final MetricsCalculator metricsCalculator;

    @Autowired
    public DetectorCalculatorBean(AggregationStrategyFactory aggregationStrategyFactory,
                                  MetricsCalculator metricsCalculator) {
        this.aggregationStrategyFactory = aggregationStrategyFactory;
        this.metricsCalculator = metricsCalculator;
    }

    @Override
    public DetectorIntersectionResult process(DetectorTemplate template, IntersectionInternalVO intersection) {
        Pair<String, String> chartDataJson = detectorReportJson(template, intersection);
        return DetectorIntersectionResult.builder().intId(intersection.getId()).intName(intersection.getName())
                .chartDataDetail(chartDataJson.getFirst()).chartDataAggregate(chartDataJson.getSecond()).build();
    }

    private Pair<String, String> detectorReportJson(DetectorTemplate template, IntersectionInternalVO intersection) {
        List<LocalDateTime> targetTimes = getTargetPeriods(template, intersection);

        AggregationStrategy strategy = aggregationStrategyFactory.getStrategy(template.getAggregation());
        List<DetectorReportChartVO> detectorReportCharts = strategy.aggregatePeriods(template, intersection,
                targetTimes);

        String detailJsonData;
        try {
            detailJsonData = createDetailDataReport(template, intersection, detectorReportCharts);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        String aggregateJsonData;
        try {
            aggregateJsonData = createAggregateDataReport(template, intersection, strategy, detectorReportCharts,
                    targetTimes);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return Pair.of(detailJsonData, aggregateJsonData);
    }

    private String createAggregateDataReport(DetectorTemplate template,
                                             IntersectionInternalVO intersection,
                                             AggregationStrategy strategy,
                                             List<DetectorReportChartVO> detectorReportCharts,
                                             List<LocalDateTime> targetTimes)
            throws JsonProcessingException {
        DetectorReportChartVO aggregatedReport = strategy.combinePeriodReports(detectorReportCharts);
        LocalTime fromTime = template.getStartTime();
        LocalTime toTime = template.getEndTime();
        long numberOfDays = DateUtils.getNumberOfDays(targetTimes);
        String aggregationType = template.getAggregation().toString();
        List<DetectorMetric> metric = template.getMetric();
        if (Objects.nonNull(aggregatedReport.getDetectorDataMap())) {
            for (DetectorDataVO detector : aggregatedReport.getDetectorDataMap().values()) {
                for (DetectorPhaseDataVO phase : detector.getDetectorPhaseDataMap().values()) {
                    phase.summaryAndCheckCondition(metric, aggregationType, numberOfDays, fromTime, toTime);
                }
            }
        }

        // Calculate and summary metrics
        removeUnselectedPhases(aggregatedReport, template.getPhases());
        metricsCalculator.calculateAndSetSummaryMetrics(aggregatedReport, metric);
//        addMetadata(template, aggregatedReport, intersection, fromDate.atStartOfDay(), toDate.atStartOfDay());
        return BeanFinder.getDefaultObjectMapper().writeValueAsString(aggregatedReport);
    }

    private String createDetailDataReport(DetectorTemplate template,
                                          IntersectionInternalVO intersection,
                                          List<DetectorReportChartVO> detectorReportCharts)
            throws JsonProcessingException {
        long numberOfHours = Duration.between(template.getStartTime(), template.getEndTime()).toHours();
        DetectorReportDetailChartVO detectorReportDetailChartVO = DetectorReportDetailChartVO.builder()
                .data(createDetailChart(detectorReportCharts, template.getPhases(), numberOfHours))
                .intId(intersection.getId()).intName(intersection.getName()).build();
        return BeanFinder.getDefaultObjectMapper().writeValueAsString(detectorReportDetailChartVO);
    }

    private void addMetadata(DetectorTemplate template,
                             DetectorReportChartVO detectorReportChartVO,
                             IntersectionInternalVO intersection,
                             LocalDateTime fromTime,
                             LocalDateTime toTime) {
        detectorReportChartVO.setIntId(intersection.getId());
        detectorReportChartVO.setIntName(intersection.getName());
        detectorReportChartVO.setFromTime(fromTime);
        detectorReportChartVO.setToTime(toTime);
        detectorReportChartVO.setStatisticMetrics(template.getMetricsJson());
    }

    private List<DetectorDetailDataVO> createDetailChart(List<DetectorReportChartVO> reportCharts,
                                                         List<Integer> phases,
                                                         long numberOfHours) {
        List<DetectorDetailDataVO> detectorDetailDataVOS = new ArrayList<>();
        for (DetectorReportChartVO reportChartVO : reportCharts) {
            DetectorDetailDataVO detailChartVO = DetectorDetailDataVO.builder().fromTime(reportChartVO.getFromTime())
                    .toTime(reportChartVO.getToTime())
                    .phaseData(createDetailPhaseDataMap(reportChartVO.getDetectorDataMap(), phases, numberOfHours))
                    .build();
            detectorDetailDataVOS.add(detailChartVO);
        }
        return detectorDetailDataVOS;
    }

    private Map<Integer, List<DetectorDetailPhaseVO>> createDetailPhaseDataMap(Map<Integer, DetectorDataVO> reportPhaseDataMap,
                                                                               List<Integer> selectedPhases,
                                                                               long numberOfHours) {
        Map<Integer, List<DetectorDetailPhaseVO>> detailPhaseDataMap = new HashMap<>();
        reportPhaseDataMap.forEach((detectorId, detectorDataVO) -> detectorDataVO.getDetectorPhaseDataMap()
                .forEach((phaseId, phaseDataVO) -> {
                    if (selectedPhases.contains(phaseId)) {
                        detailPhaseDataMap.computeIfAbsent(phaseId, k -> new ArrayList<>());
                        DetectorDetailPhaseVO detailPhaseVO = DetectorDetailPhaseVO.builder().detector(detectorId)
                                .occupancy(phaseDataVO.getOccupancy()).totalActivation(phaseDataVO.getTotalActivation())
                                .avgVolume(phaseDataVO.getAvgVolume() / numberOfHours).build();
                        detailPhaseDataMap.get(phaseId).add(detailPhaseVO);
                    }
                }));
        return detailPhaseDataMap;
    }

    private void removeUnselectedPhases(DetectorReportChartVO detectorReportChartVO, List<Integer> selectedPhases) {
        if (!CollectionUtils.isEmpty(detectorReportChartVO.getCallPhases())) {
            detectorReportChartVO.getCallPhases().retainAll(selectedPhases);

        }
        Map<Integer, DetectorDataVO> detectorDataMap = detectorReportChartVO.getDetectorDataMap();
        if (!CollectionUtils.isEmpty(detectorDataMap)) {
            detectorDataMap.entrySet().removeIf(entry -> {
                Map<Integer, DetectorPhaseDataVO> phaseDataMap = entry.getValue().getDetectorPhaseDataMap();
                phaseDataMap.keySet().removeIf(phaseId -> !selectedPhases.contains(phaseId));
                return phaseDataMap.isEmpty();
            });
        }
    }

    /**
     * Retrieves the target times for the detector report based on the template and intersection.
     *
     * @param template     The detector template
     * @param intersection The intersection
     * @return List of target periods with start and end times
     */
    private List<LocalDateTime> getTargetPeriods(DetectorTemplate template,
                                                 IntersectionInternalVO intersection) {
        String templateTimezone = template.getTimezone();
        String intersectionTimezone = intersection.getTimezone();
        Pair<LocalDate, LocalDate> datePeriod = template.getDateRange()
                .resolvePeriod(ZoneId.of(templateTimezone), AnalysisType.DETECTOR_REPORT);

        LocalDate fromDate = datePeriod.getFirst();
        LocalDate toDate = datePeriod.getSecond();

        List<LocalDateTime> targetDateTimes = new ArrayList<>();
        for (LocalDate date = fromDate; !date.isAfter(toDate); date = date.plusDays(1)) {
            LocalDateTime fromTime = date.atTime(template.getStartTime());
            LocalDateTime toTime = date.atTime(template.getEndTime());
            LocalDateTime iterDateTime = fromTime;
            while (!iterDateTime.isAfter(toTime)) {
                targetDateTimes.add(iterDateTime);
                iterDateTime = iterDateTime.plusHours(1);
                targetDateTimes.add(DateUtils.convertTimezone(iterDateTime, templateTimezone, intersectionTimezone));
            }
        }

        return targetDateTimes.stream().distinct()
                .filter(dt -> template.getWeekDays().contains(dt.getDayOfWeek()))
                .sorted().toList();
    }

}
