package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
@SuppressWarnings("all")
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        log.info("Starting weekly aggregation for {} target date times", targetDateTimes.size());

        if (isEmptyWeekDays(template.getWeekDays())) {
            log.warn("No week days specified in template, returning empty result");
            return createEmptyResultList();
        }

        List<Pair<LocalDateTime, LocalDateTime>> timeDays = targetDayTimeRanges(targetDateTimes);
        List<List<Pair<LocalDateTime, LocalDateTime>>> timeWeeks = targetWeekTimeRanges(timeDays);
        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        List<DetectorReportChartVO> weeklyReports = new ArrayList<>();
        for (List<Pair<LocalDateTime, LocalDateTime>> timeDaysInWeek : timeWeeks) {
            if (timeDaysInWeek.isEmpty()) {
                continue;
            }

            Optional<DetectorReportChartVO> detectorReportChartVO = processWeeklyPeriod(timeDaysInWeek,
                    filterDetectorIds, agencyId, intersectionId);
            if (detectorReportChartVO.isPresent()) {
                weeklyReports.add(detectorReportChartVO.get());
            }
        }

        log.info("Completed weekly aggregation, generated {} weekly reports", weeklyReports.size());
        return weeklyReports;
    }

    /**
     * Simplified parsing of target date times into daily time period pairs.
     * Takes a list of LocalDateTime objects and creates pairs representing daily time periods.
     *
     * @param targetDateTimes List of target date times to be paired
     * @return List of time period pairs, each representing a daily time range
     */
    private List<Pair<LocalDateTime, LocalDateTime>> targetDayTimeRanges(List<LocalDateTime> targetDateTimes) {
        if (targetDateTimes == null || targetDateTimes.isEmpty()) {
            log.warn("Empty or null target date times provided, returning empty list");
            return List.of();
        }

        log.debug("Parsing {} target date times into daily time pairs", targetDateTimes.size());

        return targetDateTimes.stream()
                .sorted()
                .collect(Collectors.groupingBy(LocalDateTime::toLocalDate))
                .entrySet()
                .stream()
                .map(this::createDailyTimePair)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted((p1, p2) -> p1.getFirst().compareTo(p2.getFirst()))
                .toList();
    }

    /**
     * Creates a time pair for a single date from available times.
     * Takes the earliest and latest times for the date to create the pair.
     *
     * @param dateTimeEntry Map entry containing date and list of times for that date
     * @return Optional containing the time pair if valid, empty otherwise
     */
    private Optional<Pair<LocalDateTime, LocalDateTime>> createDailyTimePair(
            Map.Entry<LocalDate, List<LocalDateTime>> dateTimeEntry) {

        LocalDate date = dateTimeEntry.getKey();
        List<LocalDateTime> timesForDate = dateTimeEntry.getValue()
                .stream()
                .sorted()
                .toList();

        if (timesForDate.size() < 2) {
            log.warn("Insufficient times for date {}: found {}, need at least 2",
                    date, timesForDate.size());
            return Optional.empty();
        }

        LocalDateTime startTime = timesForDate.get(0);
        LocalDateTime endTime = timesForDate.get(timesForDate.size() - 1);

        log.debug("Created time pair for {}: {} to {}", date, startTime, endTime);
        return Optional.of(Pair.of(startTime, endTime));
    }

    /**
     * Groups daily time pairs into weekly sublists.
     * Each week runs from Monday to Sunday, with pairs grouped accordingly.
     *
     * @param dailyPairs List of daily time pairs to group by week
     * @return List of weekly groups, each containing daily pairs for that week
     */
    private List<List<Pair<LocalDateTime, LocalDateTime>>> targetWeekTimeRanges(
            List<Pair<LocalDateTime, LocalDateTime>> dailyPairs) {

        log.debug("Grouping {} daily pairs into weekly sublists", dailyPairs.size());

        Map<LocalDate, List<Pair<LocalDateTime, LocalDateTime>>> weeklyGroups = dailyPairs.stream()
                .collect(Collectors.groupingBy(
                        pair -> getWeekStart(pair.getFirst().toLocalDate()),
                        TreeMap::new,
                        Collectors.toList()
                ));

        List<List<Pair<LocalDateTime, LocalDateTime>>> result = new ArrayList<>(weeklyGroups.values());

        log.debug("Created {} weekly groups from {} daily pairs", result.size(), dailyPairs.size());
        return result;
    }

    /**
     * Processes a single weekly period, collecting and aggregating daily reports for all applicable days.
     * Only processes days that are specified in the template's week days configuration.
     *
     * @param dailyTimeRangeInWeek List of daily time period pairs for this week
     * @param filterDetectorIds    List of detector IDs to filter by
     * @param agencyId             The agency ID
     * @param intersectionId       The intersection ID
     * @return Optional containing the aggregated weekly report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeeklyPeriod(
            List<Pair<LocalDateTime, LocalDateTime>> dailyTimeRangeInWeek,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {
        LocalDate weekStart = getWeekStart(dailyTimeRangeInWeek.get(0).getFirst().toLocalDate());
        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);

        List<DetectorReportChartVO> dailyReportsInWeek = collectDailyReportsForWeek(dailyTimeRangeInWeek,
                filterDetectorIds, agencyId, intersectionId);

        if (dailyReportsInWeek.isEmpty()) {
            log.debug("No data found for week {} to {} after applying week days filter", weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Aggregating {} daily reports for week {} to {}", dailyReportsInWeek.size(), weekStart, weekEnd);

        DetectorReportChartVO weeklyAggregatedReport = combinePeriodReports(dailyReportsInWeek);
        calculateWeeklyMetrics(weeklyAggregatedReport);
        setWeeklyReportTimeBoundaries(weeklyAggregatedReport, weekStart, weekEnd);

        log.info("Successfully created weekly report for {} to {} with {} days of data",
                weekStart, weekEnd, dailyReportsInWeek.size());

        return Optional.of(weeklyAggregatedReport);
    }

    /**
     * Collects daily detector reports for each time period pair in the week.
     * Only processes days that are specified in the template's week days configuration.
     * Uses pre-parsed daily time period pairs for precise time range handling.
     *
     * @param dailyTimeRangeInWeek List of daily time period pairs for this week
     * @param filterDetectorIds    List of detector IDs to filter by
     * @param agencyId             The agency ID
     * @param intersectionId       The intersection ID
     * @return List of daily reports for applicable days in the week
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            List<Pair<LocalDateTime, LocalDateTime>> dailyTimeRangeInWeek,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();

        // Process each time pair directly
        for (Pair<LocalDateTime, LocalDateTime> timePair : dailyTimeRangeInWeek) {
            LocalDate date = timePair.getFirst().toLocalDate();
            Optional<DetectorReportChartVO> dailyReport = collectDailyReportForDate(
                    date, timePair, filterDetectorIds, agencyId, intersectionId);

            if (dailyReport.isPresent()) {
                dailyReports.add(dailyReport.get());
                log.debug("Added daily report for {} with data", date);
            }
        }
        return dailyReports;
    }

    /**
     * Collects detector report data for a single date using a pre-parsed time period pair.
     * Uses the provided start and end times directly from the time pair.
     * Handles potential errors gracefully and provides detailed logging.
     *
     * @param date              The date to collect data for
     * @param timePair          The time period pair containing start and end times for this date
     * @param filterDetectorIds List of detector IDs to filter by
     * @param agencyId          The agency ID
     * @param intersectionId    The intersection ID
     * @return Optional containing the daily report if successful, empty if no data or error
     */
    private Optional<DetectorReportChartVO> collectDailyReportForDate(
            LocalDate date,
            Pair<LocalDateTime, LocalDateTime> timePair,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        try {
            LocalDateTime fromTime = timePair.getFirst();
            LocalDateTime toTime = timePair.getSecond();

            log.debug("Analyzing detector data for {} ({}) from {} to {} (using time pair)",
                    date, date.getDayOfWeek(), fromTime, toTime);

            Optional<DetectorReportChartVO> result = detectorReportAnalysis(
                    agencyId, intersectionId, fromTime, toTime, filterDetectorIds);

            if (result.isEmpty()) {
                log.debug("No detector data found for {} ({})", date, date.getDayOfWeek());
            }

            return result;

        } catch (Exception e) {
            log.warn("Error collecting daily report for {} ({}): {}",
                    date, date.getDayOfWeek(), e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the aggregated report.
     * This method triggers the weekly calculation logic for each phase data.
     *
     * @param report The aggregated weekly report to calculate metrics for
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        if (report.getDetectorDataMap() == null) {
            log.warn("No detector data map found in report, skipping weekly metrics calculation");
            return;
        }

        log.debug("Calculating weekly metrics for {} detectors", report.getDetectorDataMap().size());

        report.getDetectorDataMap().forEach((detectorId, detectorData) -> {
            if (detectorData.getDetectorPhaseDataMap() != null) {
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) -> {
                    log.debug("Calculating weekly metrics for detector {} phase {}", detectorId, phaseId);
                    phaseData.calculateReportWeekly();
                });
            }
        });

        log.debug("Completed weekly metrics calculation");
    }

    /**
     * Sets the time boundaries for the weekly aggregated report.
     * The report covers from the start of the week (Monday 00:00) to the end of the week (Sunday 23:59:59).
     *
     * @param report    The weekly report to set boundaries for
     * @param weekStart The Monday date of the week
     * @param weekEnd   The Sunday date of the week
     */
    private void setWeeklyReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {

        LocalDateTime fromTime = weekStart.atStartOfDay();
        LocalDateTime toTime = weekEnd.plusDays(1).atStartOfDay();

        report.setFromTime(fromTime);
        report.setToTime(toTime);

        log.debug("Set weekly report time boundaries: {} to {}", fromTime, toTime);
    }

    /**
     * Gets the start of the week (Monday) for a given date.
     * This is a utility method used for consistent week boundary calculation.
     *
     * @param date The date to find the week start for
     * @return The Monday of the week containing the given date
     */
    private LocalDate getWeekStart(LocalDate date) {
        return date.with(DayOfWeek.MONDAY);
    }

}
