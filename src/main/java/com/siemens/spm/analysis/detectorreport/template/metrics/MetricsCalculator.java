package com.siemens.spm.analysis.detectorreport.template.metrics;

import com.siemens.spm.analysis.domain.DetectorMetric;
import com.siemens.spm.analysis.vo.detectorreport.DetectorDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorPhaseDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Utility class for calculating metrics from detector data.
 * Provides methods for calculating various metrics and evaluating conditions.
 */
@Component
@Slf4j
public class MetricsCalculator {

    /**
     * Calculates summary metrics across all detectors and sets them on the report chart
     *
     * @param aggregatedReport The report chart to update with calculated metrics
     * @param detectorMetrics  List of detector metrics used for condition evaluation
     */
    public void calculateAndSetSummaryMetrics(DetectorReportChartVO aggregatedReport,
                                              List<DetectorMetric> detectorMetrics) {
        if (aggregatedReport == null || detectorMetrics == null) {
            log.warn("Cannot calculate summary metrics: null aggregatedReport or detectorMetrics");
            return;
        }

        Map<Integer, DetectorDataVO> detectorDataMap = aggregatedReport.getDetectorDataMap();
        if (detectorDataMap == null || detectorDataMap.isEmpty()) {
            log.debug("No detector data available for summary metrics calculation");
            return;
        }

        // Calculate metrics
        long totalActivation = calculateTotalActivation(detectorDataMap);
        double averageOccupancy = calculateAverageOccupancy(detectorDataMap);
        double totalAverageVolume = calculateTotalAverageVolume(detectorDataMap);

        // Set calculated metrics on the report chart
        aggregatedReport.setOccupancy(averageOccupancy);
        aggregatedReport.setTotalActivation(totalActivation);
        aggregatedReport.setAvgVolume(totalAverageVolume);

        // Create metric map for condition evaluation
        Map<DetectorMetric.Metric, DetectorMetric> metricMap = createMetricMap(detectorMetrics);

        // Evaluate conditions
        evaluateMetricConditions(aggregatedReport, metricMap);
    }

    /**
     * Creates a map of metrics for faster lookup during condition evaluation
     */
    private Map<DetectorMetric.Metric, DetectorMetric> createMetricMap(List<DetectorMetric> detectorMetrics) {
        return detectorMetrics.stream()
                .collect(Collectors.toMap(
                        metric -> DetectorMetric.Metric.resolve(metric.getKeyName(), metric.getUnit()),
                        metric -> metric,
                        (existing, replacement) -> existing));
    }

    /**
     * Evaluates whether metrics satisfy their conditions
     */
    private void evaluateMetricConditions(DetectorReportChartVO report,
                                          Map<DetectorMetric.Metric, DetectorMetric> metricMap) {
        report.evaluateAvgVolumeSatisfyCondition(metricMap.get(DetectorMetric.Metric.VOLUME));
        report.evaluateOccupancySatisfyCondition(metricMap.get(DetectorMetric.Metric.OCCUPANCY));
        report.evaluateTotalActSatisfyCondition(metricMap.get(DetectorMetric.Metric.ACTIVATION));
    }

    /**
     * Calculates the total activation count across all detectors
     * <p>
     * (Sum of detector hits in selected time range in selected date range) = V
     *
     * @param detectorDataMap Map of detector data
     * @return Total activation count
     */
    public long calculateTotalActivation(Map<Integer, DetectorDataVO> detectorDataMap) {
        return detectorDataMap.values().stream()
                .map(this::getPhaseData)
                .filter(Objects::nonNull)
                .mapToLong(phaseData -> phaseData.getTotalActivation() != null ?
                        phaseData.getTotalActivation() : 0)
                .sum();
    }

    /**
     * Calculates the average occupancy across all detector phase data
     * <p>
     * <p>
     * "OccupancyTime =  Duration.between(detectorOnTime, detectorOffTime).toMillis() / 1000.0
     * (sum of all occupancy times /(x.y)) * 100"
     *
     * @param detectorDataMap Map of detector data
     * @return Average occupancy
     */
    public double calculateAverageOccupancy(Map<Integer, DetectorDataVO> detectorDataMap) {
        return detectorDataMap.values().stream()
                .map(this::getPhaseData)
                .filter(Objects::nonNull)
                .mapToDouble(DetectorPhaseDataVO::getOccupancy)
                .average()
                .orElse(0.0);
    }

    /**
     * Calculates the total average volume across all detectors
     *
     * @param detectorDataMap Map of detector data
     * @return Total average volume
     */
    public double calculateTotalAverageVolume(Map<Integer, DetectorDataVO> detectorDataMap) {
        return detectorDataMap.values().stream()
                .map(this::getPhaseData)
                .filter(Objects::nonNull)
                .mapToDouble(phaseData -> phaseData.getAvgVolume() != null ? phaseData.getAvgVolume() : 0)
                .sum();
    }

    /**
     * Gets the first phase data from a detector data object
     *
     * @param detectorDataVO Detector data value object
     * @return First phase data or null if none exists
     */
    private DetectorPhaseDataVO getPhaseData(DetectorDataVO detectorDataVO) {
        if (detectorDataVO == null || detectorDataVO.getDetectorPhaseDataMap() == null) {
            return null;
        }

        return detectorDataVO.getDetectorPhaseDataMap().values().stream()
                .findFirst()
                .orElse(null);
    }

}
