package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.siemens.spm.analysis.api.vo.response.AnalysisResultObject;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorPhaseDataVO;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.common.util.BeanFinder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.time.DayOfWeek;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Base abstract class for aggregation strategies.
 * Contains common functionality used by all concrete strategy implementations.
 */
@Slf4j
public abstract class BaseAggregationStrategy implements AggregationStrategy {

    protected final DetectorReportStrategy detectorReportStrategy;

    protected BaseAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        this.detectorReportStrategy = detectorReportStrategy;
    }

    /**
     * Performs detector report analysis for a specific time range.
     *
     * @param agencyId The agency ID
     * @param intId    The intersection ID
     * @param fromTime The start time
     * @param toTime   The end time
     * @return Optional containing the detector report chart if successful, empty otherwise
     */
    protected Optional<DetectorReportChartVO> detectorReportAnalysis(
            Integer agencyId,
            String intId,
            LocalDateTime fromTime,
            LocalDateTime toTime, List<Long> filterDetectorIds) {
        AnalysisResultObject<DetectorReportChartVO> resultObject = detectorReportStrategy.getDetectorReport(
                agencyId, intId, fromTime, toTime, filterDetectorIds);
        if (resultObject.getStatusCode().equals(AnalysisResultObject.StatusCode.SUCCESS)) {
            return (resultObject.getData().getChartList() != null && !resultObject.getData().getChartList().isEmpty())
                    ? Optional.ofNullable(resultObject.getData().getChartList().get(0))
                    : Optional.empty();
        }
        return Optional.empty();
    }

    /**
     * Validates the week days configuration in the template.
     *
     * @return True if week days are valid, false otherwise
     */
    protected boolean isEmptyWeekDays(Set<DayOfWeek> weekDays) {
        if (CollectionUtils.isEmpty(weekDays)) {
            log.warn("No week days specified in template");
            return true;
        }
        return false;
    }

    /**
     * Creates an empty result list.
     *
     * @return Empty list of detector report charts
     */
    protected List<DetectorReportChartVO> createEmptyResultList() {
        return new ArrayList<>();
    }

    /**
     * Aggregates multiple period detector reports into a single consolidated report.
     * This method combines data from all provided reports, preserving all detector and phase information.
     *
     * @param totalReports List of detector reports to aggregate
     * @return A single aggregated detector report
     */
    public DetectorReportChartVO combinePeriodReports(List<DetectorReportChartVO> totalReports) {
        if (totalReports == null || totalReports.isEmpty()) {
            log.debug("No reports to aggregate");
            return new DetectorReportChartVO();
        }

        DetectorReportChartVO aggregatedReport = new DetectorReportChartVO();
        Map<Integer, DetectorDataVO> aggregatedDataMap = new HashMap<>();

        // Collect all call phases from all reports
        Set<Integer> callPhases = totalReports.stream()
                .flatMap(report -> report.getCallPhases().stream())
                .collect(Collectors.toSet());

        log.debug("Aggregating {} reports with {} total call phases", totalReports.size(), callPhases.size());

        for (DetectorReportChartVO report : totalReports) {
            for (Map.Entry<Integer, DetectorDataVO> entry : report.getDetectorDataMap().entrySet()) {
                Integer detectorId = entry.getKey();
                DetectorDataVO periodData = entry.getValue();

                log.debug("Aggregating data for detector {}", detectorId);

                // Get or create the aggregated detector data
                DetectorDataVO aggregatedDetectorData = aggregatedDataMap.computeIfAbsent(detectorId,
                        k -> new DetectorDataVO());

                // Set detector metadata
                aggregatedDetectorData.setDetectorType(
                        periodData.getDetectorType() != null ? periodData.getDetectorType() : null);

                aggregatedDetectorData.setDistanceToStopBar(periodData.getDistanceToStopBar() != null ?
                        periodData.getDistanceToStopBar() : null);

                // Aggregate phase data
                periodData.getDetectorPhaseDataMap().forEach((phaseId, dailyPhaseData) -> {
                    DetectorPhaseDataVO aggregatedPhaseData = aggregatedDetectorData.getDetectorPhaseDataMap()
                            .computeIfAbsent(phaseId, k -> new DetectorPhaseDataVO(phaseId));
                    log.debug("Aggregating data for detector {} phase {}", detectorId, phaseId);
                    aggregatedPhaseData.aggregateWithData(dailyPhaseData);
                });
            }
        }

        aggregatedReport.setDetectorDataMap(aggregatedDataMap);
        aggregatedReport.setCallPhases(callPhases);
        log.debug("Completed aggregation of {} reports", totalReports.size());
        return aggregatedReport;
    }

    protected List<Long> getFilterDetectorIds(String detectorsJson) {
        try {
            return BeanFinder.getDefaultObjectMapper()
                    .readValue(detectorsJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            log.error("Failed to parse detectors json", e);
            throw new RuntimeException(e);
        }
    }

}
