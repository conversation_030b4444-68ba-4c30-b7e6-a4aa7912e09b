package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Strategy for hourly aggregation of detector reports.
 * Processes data in hourly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class HourlyAggregationStrategy extends BaseAggregationStrategy {

    private static final int HOUR_INTERVAL = 1;

    public HourlyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(DetectorTemplate template,
                                                        IntersectionInternalVO intersection,
                                                        List<LocalDateTime> targetDateTimes) {
        List<DetectorReportChartVO> resultCharts = createEmptyResultList();

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        for (LocalDateTime targetTime : targetDateTimes) {
            LocalDateTime nextHour = targetTime.plusHours(HOUR_INTERVAL);
            log.info("Processing hourly data with time range: {} to {}", targetTime, nextHour);
            buildHourlyResultCharts(template, intersection, targetTime, nextHour, filterDetectorIds, resultCharts);
        }

        return resultCharts;
    }

    /**
     * Builds hourly result charts by analyzing detector report data for a specific time range.
     */
    private void buildHourlyResultCharts(DetectorTemplate template,
                                         IntersectionInternalVO intersection,
                                         LocalDateTime iterDateTime,
                                         LocalDateTime nextHour,
                                         List<Long> filterDetectorIds,
                                         List<DetectorReportChartVO> resultCharts) {
        detectorReportAnalysis(template.getAgencyId(), intersection.getId(), iterDateTime, nextHour,
                filterDetectorIds).ifPresent(detectorReportChartVO -> {
            detectorReportChartVO.setFromTime(iterDateTime);
            detectorReportChartVO.setToTime(nextHour);
            resultCharts.add(detectorReportChartVO);
        });
    }

}