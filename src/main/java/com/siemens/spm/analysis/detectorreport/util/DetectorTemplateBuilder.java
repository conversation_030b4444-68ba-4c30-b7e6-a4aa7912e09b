package com.siemens.spm.analysis.detectorreport.util;

import java.time.DayOfWeek;
import java.util.Set;
import java.util.TreeSet;

import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateCoreDataVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateGeneralVO;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.common.agency.utils.AgencyUtils;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DetectorTemplateBuilder {

    private DetectorTemplateBuilder() {}

    public static DetectorTemplateDetailVO buildDetailVOFromEntity(DetectorTemplate template) {
        DetectorTemplateDetailVO detailVO = buildSimpleVOFromEntity(template);
        detailVO.setGeneralVO(buildGeneralDataFromEntity(template));
        // Update schedule data
        TemplateSchedule schedule = template.getSchedule();
        if (schedule != null) {
            detailVO.setScheduleVO(DetectorScheduleBuilder.buildVOFromEntity(schedule));
        }

        // Update intersections data
        String intersectionIdsString = template.getIntersectionList();
        IntersectionIdsVO intersectionIdsVO;
        if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionIdsString)) {
            intersectionIdsVO = IntersectionIdsVO.resolveAllIntersection();
        } else {
            intersectionIdsVO = IntersectionIdsVO.resolveSpecificIntersectionFromString(intersectionIdsString);
        }
        detailVO.setIntersectionIdsVO(intersectionIdsVO);
        detailVO.getScheduleVO().setMailReceive(template.isMailReceived());
        return detailVO;
    }

    public static DetectorTemplateDetailVO buildSimpleVOFromEntity(DetectorTemplate template) {
        return DetectorTemplateDetailVO.builder()
                .id(template.getId())
                .status(template.getStatus())
                .ownerId(template.getOwnerId())
                .createdAt(template.getCreatedAt())
                .ownerVO(new SimpleOwnerVO(template.getOwnerId()))
                .build();
    }

    public static DetectorTemplateCoreDataVO buildCoreSimpleVOFromEntity(DetectorTemplate template) {
        return DetectorTemplateCoreDataVO.builder()
                .id(template.getId())
                .status(template.getStatus())
                .ownerId(template.getOwnerId())
                .createdAt(template.getCreatedAt())
                .ownerVO(new SimpleOwnerVO(template.getOwnerId()))
                .build();
    }

    public static DetectorTemplateGeneralVO buildGeneralDataFromEntity(DetectorTemplate template) {
        Set<DayOfWeek> weekDays = Set.of();
        if (template.getWeekDays() != null) {
            weekDays = new TreeSet<>(template.getWeekDays());
        }
        
        return DetectorTemplateGeneralVO.builder()
                .name(template.getName())
                .description(template.getDescription())
                .phases(template.getPhases())
                .dateRange(template.getDateRange())
                .detectors(template.getDetectors())
                .weekDays(weekDays)
                .agencyId(Integer.valueOf(AgencyUtils.getAgencyId()))
                .aggregation(template.getAggregation())
                .startTime(template.getStartTime())
                .endTime(template.getEndTime())
                .timeZone(template.getTimezone())
                .timezoneId(template.getTimezoneId())
                .detectorScope(template.getDetectorScope())
                .metrics(DetectorMetricBuilder.buildVOFromEntity(template.getMetric()))
                .build();

    }

    public static DetectorTemplate buildEntityFromCreateRequestVO(DetectorTemplateCreateRequestVO requestVO) {
        DetectorTemplate detectorTemplate = DetectorTemplate.builder()
                .name(requestVO.getGeneralVO().getName())
                .description(requestVO.getGeneralVO().getDescription())
                .agencyId(Integer.valueOf(AgencyUtils.getAgencyId()))
                .detectorScope(requestVO.getGeneralVO().getDetectorScope())
                .startTime(requestVO.getGeneralVO().getStartTime())
                .endTime(requestVO.getGeneralVO().getEndTime())
                .timezone(requestVO.getGeneralVO().getTimeZone())
                .aggregation(requestVO.getGeneralVO().getAggregation())
                .dateRange(requestVO.getGeneralVO().getDateRange())
                .weekDays(requestVO.getGeneralVO().getWeekDays())
                .timezoneId(requestVO.getGeneralVO().getTimezoneId())
                .metric(DetectorMetricBuilder.buildEntityFromVOList(requestVO.getGeneralVO().getMetrics()))
                .phases(requestVO.getGeneralVO().getPhases())
                .detectors(requestVO.getGeneralVO().getDetectors())
                .intersectionList(IntersectionIdsVO.resolveIntersectionIdsString(requestVO.getIntersectionIdsVO()))
                .status(TemplateStatus.ACTIVE)
                .deleted(false)
                .build();
        try {
            detectorTemplate.persistTransientFields();
        } catch (Exception e) {
            log.error("DetectorTemplate is missing data before persist to DB");
            throw new IllegalArgumentException("DetectorTemplate is missing data before persist to DB");
        }
        return detectorTemplate;
    }

    public static void updateVOToEntity(DetectorTemplate template, DetectorTemplateUpdateCoreDataRequestVO requestVO) {
        template.setName(requestVO.getName());
        template.setDescription(requestVO.getDescription());
        template.setAgencyId(Integer.valueOf(AgencyUtils.getAgencyId()));
        template.setWeekDays(requestVO.getWeekDays());
        template.setTimezone(requestVO.getTimeZone());
        template.setStartTime(requestVO.getStartTime());
        template.setEndTime(requestVO.getEndTime());
        template.setAggregation(requestVO.getAggregation());
        template.setDateRange(requestVO.getDateRange());
        template.setMetric(DetectorMetricBuilder.buildEntityFromVOList(requestVO.getMetrics()));
        template.setPhases(requestVO.getPhases());
        template.setDetectors(requestVO.getDetectors());
        template.setDetectorScope(requestVO.getDetectorScope());
        template.setTimezoneId(requestVO.getTimezoneId());
        try {
            template.persistTransientFields();
        } catch (Exception e) {
            log.error("DetectorTemplate is missing data before persist to DB");
            throw new IllegalArgumentException("DetectorTemplate is missing data before persist to DB");
        }
    }
}
