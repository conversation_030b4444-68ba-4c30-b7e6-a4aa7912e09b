package com.siemens.spm.analysis.detectorreport.template;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.querydsl.core.types.OrderSpecifier;
import com.siemens.spm.analysis.api.constant.TemplateConstant;
import com.siemens.spm.analysis.api.vo.DateRange;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateCoreDataVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateDetailVO;
import com.siemens.spm.analysis.api.vo.detectorreport.DetectorTemplateScheduleVO;
import com.siemens.spm.analysis.api.vo.enums.ScheduleScope;
import com.siemens.spm.analysis.api.vo.enums.TemplateStatus;
import com.siemens.spm.analysis.api.vo.request.IntersectionIdsRequestVO;
import com.siemens.spm.analysis.api.vo.request.TemplateIntersectionSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateActivateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateCreateRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateSearchRequestVO;
import com.siemens.spm.analysis.api.vo.request.detectorreport.DetectorTemplateUpdateCoreDataRequestVO;
import com.siemens.spm.analysis.api.vo.response.IntersectionSearchResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResponseVO;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorMetricResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorScheduleResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateCoreDataResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateDetailResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateManipulateResultObject;
import com.siemens.spm.analysis.api.vo.response.detectorreport.DetectorTemplateSearchResultObject;
import com.siemens.spm.analysis.config.ReportTemplateConfig;
import com.siemens.spm.analysis.detectorreport.exception.DetectorTemplateProcessingException;
import com.siemens.spm.analysis.detectorreport.util.DetectorMetricBuilder;
import com.siemens.spm.analysis.detectorreport.util.DetectorScheduleBuilder;
import com.siemens.spm.analysis.detectorreport.util.DetectorTemplateBuilder;
import com.siemens.spm.analysis.detectorreport.util.DetectorTemplateSearchHelper;
import com.siemens.spm.analysis.domain.DetectorIntersectionResult;
import com.siemens.spm.analysis.domain.DetectorMetric;
import com.siemens.spm.analysis.domain.DetectorResult;
import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.domain.TemplateSchedule;
import com.siemens.spm.analysis.factory.TimezoneFactory;
import com.siemens.spm.analysis.performancemetric.template.exception.PMTemplateProcessingException;
import com.siemens.spm.analysis.repository.DetectorIntersectionResultRepository;
import com.siemens.spm.analysis.repository.DetectorResultRepository;
import com.siemens.spm.analysis.repository.DetectorTemplateRepository;
import com.siemens.spm.analysis.repository.TemplateScheduleRepository;
import com.siemens.spm.analysis.repository.filterdata.DetectorTemplateFilterData;
import com.siemens.spm.analysis.strategy.intercom.IntersectionStrategy;
import com.siemens.spm.analysis.strategy.intercom.ReportNotificationStrategy;
import com.siemens.spm.analysis.summaryreport.util.TemplateScheduleHelper;
import com.siemens.spm.common.agency.supports.AgencyAware;
import com.siemens.spm.common.agency.utils.AgencyUtils;
import com.siemens.spm.common.constant.IntersectionConstants;
import com.siemens.spm.common.security.SecurityUtils;
import com.siemens.spm.common.shared.domaintype.ActionTarget;
import com.siemens.spm.common.shared.domaintype.ActionType;
import com.siemens.spm.common.shared.domaintype.IntersectionScope;
import com.siemens.spm.common.shared.domaintype.IntersectionStatus;
import com.siemens.spm.common.shared.domaintype.analysis.AnalysisType;
import com.siemens.spm.common.shared.exception.InvalidSortColumnException;
import com.siemens.spm.common.shared.exception.InvalidSortOrderException;
import com.siemens.spm.common.shared.resource.TextKey;
import com.siemens.spm.common.shared.vo.ActionVO;
import com.siemens.spm.common.shared.vo.IntersectionIdsVO;
import com.siemens.spm.common.shared.vo.IntersectionSearchResponseDataVO;
import com.siemens.spm.common.shared.vo.ReportResultActionDataVO;
import com.siemens.spm.common.shared.vo.SimpleOwnerVO;
import com.siemens.spm.common.shared.vo.SimpleResultObject;
import com.siemens.spm.common.util.BeanFinder;
import com.siemens.spm.common.util.DateTimeUtils;
import com.siemens.spm.common.util.ListUtil;
import com.siemens.spm.common.util.NameUtil;
import com.siemens.spm.perflog.boundary.TaskProgressRetriever;
import com.siemens.spm.perflog.domaintype.TaskStatus;
import com.siemens.spm.spmstudiosdk.dto.StudioUserDto;
import com.siemens.spm.spmstudiosdk.exception.StudioException;
import com.siemens.spm.spmstudiosdk.service.StudioUserService;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalListVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import com.siemens.spm.usermanagementservice.api.vo.request.IntersectionSearchRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.siemens.spm.analysis.api.vo.enums.TemplateStatus.ACTIVE;

@Slf4j
@Service
public class DetectorTemplateStrategyBean implements DetectorTemplateStrategy {

    public static final String TEMPLATE_NOT_FOUND = "Can't find template with id: {}";

    private final StudioUserService studioUserService;

    private final TemplateScheduleRepository templateScheduleRepository;

    private final DetectorTemplateRepository detectorTemplateRepository;

    private final IntersectionStrategy intersectionStrategy;

    private final DetectorCalculator detectorCalculator;

    private final DetectorResultRepository detectorResultRepository;

    private final DetectorIntersectionResultRepository detectorIntersectionResultRepository;

    private final TaskProgressRetriever taskProgressRetriever;

    private final ReportTemplateConfig reportTemplateConfig;

    private final ReportNotificationStrategy reportNotificationStrategy;

    private final TimezoneFactory timezoneFactory;

    public DetectorTemplateStrategyBean(StudioUserService studioUserService,
                                        TemplateScheduleRepository templateScheduleRepository,
                                        DetectorTemplateRepository detectorTemplateRepository,
                                        IntersectionStrategy intersectionStrategy,
                                        DetectorCalculator detectorCalculator,
                                        DetectorResultRepository detectorResultRepository,
                                        DetectorIntersectionResultRepository detectorIntersectionResultRepository,
                                        TaskProgressRetriever taskProgressRetriever,
                                        ReportTemplateConfig reportTemplateConfig,
                                        ReportNotificationStrategy reportNotificationStrategy,
                                        TimezoneFactory timezoneFactory) {
        this.studioUserService = studioUserService;
        this.templateScheduleRepository = templateScheduleRepository;
        this.detectorTemplateRepository = detectorTemplateRepository;
        this.intersectionStrategy = intersectionStrategy;
        this.detectorCalculator = detectorCalculator;
        this.detectorResultRepository = detectorResultRepository;
        this.detectorIntersectionResultRepository = detectorIntersectionResultRepository;
        this.taskProgressRetriever = taskProgressRetriever;
        this.reportTemplateConfig = reportTemplateConfig;
        this.reportNotificationStrategy = reportNotificationStrategy;
        this.timezoneFactory = timezoneFactory;
    }

    private static final String STUDIO_USER_ERROR_MSG = "Error when get user by id from Studio";

    @Override
    @Transactional
    public DetectorTemplateDetailResultObject createReportTemplate(DetectorTemplateCreateRequestVO requestVO) {

        Integer agencyId = Integer.valueOf(AgencyUtils.getAgencyId());
        String email = SecurityUtils.getCurrentUserEmail();
        Optional<StudioUserDto> userDtoOptional;
        try {
            userDtoOptional = studioUserService.findByEmail(email);
        } catch (StudioException e) {
            log.error("Error when get current user", e);
            return new DetectorTemplateDetailResultObject(null, DetectorTemplateDetailResultObject.StatusCode.ERROR);
        }
        if (userDtoOptional.isEmpty()) {
            log.warn("Cannot find current user with email={} in Studio", email);
            return new DetectorTemplateDetailResultObject(null, DetectorTemplateDetailResultObject.StatusCode.ERROR);
        }

        Integer userId = userDtoOptional.get().getId();

        // Verify agency
        if (agencyId == null) {
            log.debug("Missing agency id");
            return new DetectorTemplateDetailResultObject(null,
                    DetectorTemplateDetailResultObject.StatusCode.MISSING_AGENCY_ID);
        }

        // default get timezone of agency if it couldn't receive timezone from Frontend side
        String timeZone = requestVO.getGeneralVO().getTimezoneId();
        if (StringUtils.isEmpty(timeZone)) {
            requestVO.getGeneralVO().setTimezoneId(timezoneFactory.validateAndResolveTimezone(timeZone, agencyId));
        }
        requestVO.getGeneralVO().setTimeZone(DateTimeUtils.getZoneOffSet(timeZone));

        // Verify intersections
        DetectorTemplate template = DetectorTemplateBuilder.buildEntityFromCreateRequestVO(requestVO);
        template.setAgencyId(agencyId);
        template.setOwnerId(Long.valueOf(userId));
        TemplateSchedule schedule = DetectorScheduleBuilder.buildEntityFromRequestVO(requestVO.getScheduleVO());
        schedule = templateScheduleRepository.saveAndFlush(schedule);
        template.setSchedule(schedule);
        template.setMailReceived(requestVO.getScheduleVO().isMailReceive());
        template = detectorTemplateRepository.save(template);
        DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);

        notifyOwnerAboutTemplateChange(detailVO, Long.valueOf(userId), TemplateConstant.ActionOnTemplate.CREATED);
        return new DetectorTemplateDetailResultObject(detailVO, DetectorTemplateDetailResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional(readOnly = true)
    public DetectorTemplateCoreDataResultObject getTemplateCoreData(Long templateId) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new DetectorTemplateCoreDataResultObject(null,
                    DetectorTemplateCoreDataResultObject.StatusCode.NOT_FOUND);
        }

        DetectorTemplate detectorTemplate = templateOptional.get();
        DetectorTemplateCoreDataVO templateCoreDataVO = DetectorTemplateBuilder.buildCoreSimpleVOFromEntity(
                detectorTemplate);
        templateCoreDataVO.setGeneralVO(DetectorTemplateBuilder.buildGeneralDataFromEntity(detectorTemplate));
        updateOwnerData(templateCoreDataVO.getOwnerVO());
        log.debug("Successful get report template core data! Template id: {}", templateId);
        return new DetectorTemplateCoreDataResultObject(templateCoreDataVO,
                DetectorTemplateCoreDataResultObject.StatusCode.SUCCESS);
    }

    @Override
    public DetectorMetricResultObject getAllMetrics() {
        DetectorMetricResponseVO responseVO = new DetectorMetricResponseVO(
                DetectorMetricBuilder.buildVOFromListEnum(Arrays.stream(DetectorMetric.Metric.values()).toList()));
        return new DetectorMetricResultObject(responseVO, SimpleResultObject.SimpleStatusCode.SUCCESS);
    }

    @Override
    @Transactional(readOnly = true)
    public DetectorScheduleResultObject getTemplateSchedule(Long templateId) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new DetectorScheduleResultObject(null, DetectorScheduleResultObject.StatusCode.NOT_FOUND);
        }

        DetectorTemplate template = templateOptional.get();
        DetectorTemplateScheduleVO scheduleVO = DetectorScheduleBuilder.buildVOFromEntity(template.getSchedule());
        scheduleVO.setMailReceive(template.isMailReceived());
        return new DetectorScheduleResultObject(scheduleVO, DetectorScheduleResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional(readOnly = true)
    public IntersectionSearchResultObject searchAvailableTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        Integer agencyId;
        Long templateId = searchRequestVO.getTemplateId();

        List<String> currentIntIdsList;
        if (templateId != null) {
            Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
            if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
                return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
            }

            DetectorTemplate template = templateOptional.get();
            agencyId = template.getAgencyId();
            String currentIntIdsString = template.getIntersectionList();
            if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(currentIntIdsString)) {
                String[] intIdsArr = currentIntIdsString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
                currentIntIdsList = Arrays.asList(intIdsArr);
            } else {
                IntersectionSearchResponseDataVO responseDataVO = IntersectionSearchResponseDataVO.builder()
                        .intersections(new ArrayList<>())
                        .totalCount(0L)
                        .scope(IntersectionScope.SPECIFIC_INTERSECTIONS)
                        .build();

                return IntersectionSearchResultObject.success(responseDataVO);
            }
        } else {
            agencyId = searchRequestVO.getAgencyId();
            currentIntIdsList = new ArrayList<>();
        }

        // Append excluded intersection ids from request
        String[] excludeIntIds = searchRequestVO.getExcludeIntIds();
        if (excludeIntIds != null && excludeIntIds.length > 0) {
            currentIntIdsList = Stream.concat(currentIntIdsList.stream(), Arrays.stream(excludeIntIds)).distinct()
                    .toList();
        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(agencyId)
                .orderByColumns(searchRequestVO.getOrderByColumns())
                .exclusionaryIds(currentIntIdsList)
                .shouldPaginate(searchRequestVO.getShouldPaginate())
                .page(searchRequestVO.getPage())
                .size(searchRequestVO.getSize())
                .status(IntersectionStatus.AVAILABLE.getInsight()) // Just return available intersection
                .text(searchRequestVO.getText()).build();
        IntersectionSearchResponseDataVO responseVO = intersectionStrategy.searchIntersectionsInternal(searchRequest);

        return IntersectionSearchResultObject.success(responseVO);
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject updateCoreData(Long templateId,
                                                                 DetectorTemplateUpdateCoreDataRequestVO updateRequestVO) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.NOT_FOUND);
        }

        String timeZone = updateRequestVO.getTimezoneId();
        if (StringUtils.isEmpty(timeZone)) {
            updateRequestVO.setTimezoneId(timezoneFactory.validateAndResolveTimezone(timeZone, updateRequestVO.getAgencyId()));
        }
        updateRequestVO.setTimeZone(DateTimeUtils.getZoneOffSet(timeZone));

        DetectorTemplate template = templateOptional.get();
        updateScheduleDataToManualIfNeed(template, updateRequestVO.getDateRange());
        DetectorTemplateBuilder.updateVOToEntity(template, updateRequestVO);
        detectorTemplateRepository.save(template);
        DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
        notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.UPDATE);
        return new DetectorTemplateManipulateResultObject(DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject updateTemplateScheduleData(Long templateId,
                                                                             DetectorTemplateScheduleVO templateScheduleVO) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            log.debug(TEMPLATE_NOT_FOUND, templateId);
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.NOT_FOUND);
        }
        DetectorTemplate detectorTemplate = templateOptional.get();
        TemplateSchedule templateSchedule = detectorTemplate.getSchedule();
        DetectorScheduleBuilder.updateVOToEntity(templateSchedule, templateScheduleVO);
        templateScheduleRepository.save(templateSchedule);

        detectorTemplate.setMailReceived(templateScheduleVO.isMailReceive());
        detectorTemplate = detectorTemplateRepository.save(detectorTemplate);
        DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(detectorTemplate);
        notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.UPDATE);
        return new DetectorTemplateManipulateResultObject(DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject softDeletes(List<Long> templateIds) {
        List<DetectorTemplate> templates = detectorTemplateRepository.findAllByIdIn(templateIds);
        if (templates.isEmpty()) {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.NOT_FOUND);
        }

        List<DetectorTemplate> templateToDelete = templates.stream().filter(template -> !template.isDeleted()).toList();

        for (DetectorTemplate template : templateToDelete) {
            template.setDeleted(true);
            detectorTemplateRepository.save(template);
            DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
            notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.DELETE);
        }
        log.debug("Delete successfully templates have id in {}", templateIds);
        return new DetectorTemplateManipulateResultObject(DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject addIntersections(Long templateId,
                                                                   IntersectionIdsRequestVO intersectionIdsRequestVO) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);

        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.TEMPLATE_NOT_FOUND);
        }

        DetectorTemplate template = templateOptional.get();
        List<String> intersectionIdsRequest = IntersectionIdsVO.resolveIntersectionIdsList(intersectionIdsRequestVO);

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (IntersectionScope.ALL_INTERSECTIONS.equals(intersectionScope)) {
            template.setIntersectionList(IntersectionConstants.ALL_INTERSECTION_INDICATOR);
            detectorTemplateRepository.save(template);
            DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
            notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.UPDATE);
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
        } else if (IntersectionScope.SPECIFIC_INTERSECTIONS.equals(intersectionScope)) {
            // Check case add new when current scope is ALL_INTERSECTIONS first
            if (IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(template.getIntersectionList())) {
                log.debug("Add intersection(s) scope SPECIFIC when current scope is ALL_INTERSECTIONS is illegal!");
                return new DetectorTemplateManipulateResultObject(
                        DetectorTemplateManipulateResultObject.StatusCode.INTERSECTION_SCOPE_INVALID);
            }

            List<String> currentIntersectionIdsList = Arrays.asList(
                    template.getIntersectionList().split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
            List<String> newIntersectionIdsList = new ArrayList<>(currentIntersectionIdsList);

            for (String uuid : intersectionIdsRequest) {
                if (!newIntersectionIdsList.contains(uuid)) {
                    newIntersectionIdsList.add(uuid);
                }
            }
            String newIntersectionIdsString = newIntersectionIdsList.stream().map(String::valueOf)
                    .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));

            template.setIntersectionList(newIntersectionIdsString);
            detectorTemplateRepository.save(template);
            DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
            notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.UPDATE);
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
        } else {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.INTERSECTION_SCOPE_INVALID);
        }
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject removeIntersections(Long templateId,
                                                                      IntersectionIdsRequestVO intersectionIdsRequestVO) {
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);

        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.TEMPLATE_NOT_FOUND);
        }

        DetectorTemplate template = templateOptional.get();

        IntersectionScope intersectionScope = intersectionIdsRequestVO.getScope();
        if (intersectionScope == null) {
            log.debug("Intersection scope is invalid");
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.INTERSECTION_SCOPE_INVALID);
        }

        String newIntersectionIds;

        switch (intersectionScope) {
            case ALL_INTERSECTIONS -> newIntersectionIds = "";
            case SPECIFIC_INTERSECTIONS -> {
                List<String> intersectionIdListRequest = intersectionIdsRequestVO.getUuids();
                if (ListUtil.hasNoItem(intersectionIdListRequest)) {
                    newIntersectionIds = template.getIntersectionList();
                } else {
                    newIntersectionIds = getAllIntersectionIds(template).stream()
                            .filter(uuid -> !intersectionIdListRequest.contains(uuid))
                            .collect(Collectors.joining(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
                }
            }
            default -> newIntersectionIds = template.getIntersectionList();
        }

        template.setIntersectionList(newIntersectionIds);
        detectorTemplateRepository.save(template);
        DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
        notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), TemplateConstant.ActionOnTemplate.UPDATE);
        return new DetectorTemplateManipulateResultObject(DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional
    public DetectorTemplateManipulateResultObject activateOrDeactivateTemplates(DetectorTemplateActivateRequestVO activeRequestVO) {
        if (activeRequestVO == null || activeRequestVO.getStatus() == null) {
            throw new IllegalArgumentException();
        }
        List<DetectorTemplate> templates = detectorTemplateRepository.findAllByIdIn(activeRequestVO.getTemplateIds());
        if (CollectionUtils.isEmpty(templates)) {
            return new DetectorTemplateManipulateResultObject(
                    DetectorTemplateManipulateResultObject.StatusCode.TEMPLATE_NOT_FOUND);
        }

        TemplateStatus statusToUpdate = activeRequestVO.getStatus();
        List<DetectorTemplate> templateToUpdateStatus = templates.stream().filter(template -> !template.isDeleted())
                .filter(template -> template.getStatus() != statusToUpdate).toList();

        for (DetectorTemplate template : templateToUpdateStatus) {
            template.setStatus(statusToUpdate);
            detectorTemplateRepository.save(template);
            // Send notification to user
            TemplateConstant.ActionOnTemplate action = switch (statusToUpdate) {
                case ACTIVE -> TemplateConstant.ActionOnTemplate.ACTIVATE;
                case INACTIVE -> TemplateConstant.ActionOnTemplate.DEACTIVATE;
            };

            DetectorTemplateDetailVO detailVO = DetectorTemplateBuilder.buildDetailVOFromEntity(template);
            notifyOwnerAboutTemplateChange(detailVO, detailVO.getOwnerId(), action);
        }
        return new DetectorTemplateManipulateResultObject(DetectorTemplateManipulateResultObject.StatusCode.SUCCESS);
    }

    @Override
    @Transactional(readOnly = true)
    public IntersectionSearchResultObject searchTemplateIntersections(TemplateIntersectionSearchRequestVO searchRequestVO) {
        // Verify template exist or not
        Long templateId = searchRequestVO.getTemplateId();
        Optional<DetectorTemplate> templateOptional = detectorTemplateRepository.findById(templateId);
        if (templateOptional.isEmpty() || templateOptional.get().isDeleted()) {
            return IntersectionSearchResultObject.error(IntersectionSearchResultObject.StatusCode.NOT_FOUND);
        }

        DetectorTemplate template = templateOptional.get();
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId()).orderByColumns(searchRequestVO.getOrderByColumns())
                .page(searchRequestVO.getPage()).size(searchRequestVO.getSize()).status(searchRequestVO.getStatus())
                .shouldPaginate(searchRequestVO.getShouldPaginate())
                .text(searchRequestVO.getText()).build();

        IntersectionSearchResponseDataVO responseDataVO = intersectionStrategy.searchIntersectionsInternal(
                template.getIntersectionList(), searchRequest);

        return IntersectionSearchResultObject.success(responseDataVO);
    }

    @Override
    @Transactional(readOnly = true)
    public DetectorTemplateSearchResultObject searchTemplates(DetectorTemplateSearchRequestVO requestVO) {
        Pair<List<DetectorTemplate>, Long> templatesAndCount;
        try {
            templatesAndCount = getTemplateAndCountFromDB(requestVO);
        } catch (InvalidSortColumnException ex) {
            return new DetectorTemplateSearchResultObject(null,
                    DetectorTemplateSearchResultObject.StatusCode.INVALID_SORT_COLUMN);
        } catch (InvalidSortOrderException ex) {
            return new DetectorTemplateSearchResultObject(null,
                    DetectorTemplateSearchResultObject.StatusCode.INVALID_SORT_ORDER);
        }
        List<DetectorTemplate> templates = templatesAndCount.getFirst();
        Long totalCount = templatesAndCount.getSecond();

        if (templates.isEmpty()) {
            DetectorTemplateSearchResultObject.ResponseData responseData = DetectorTemplateSearchResultObject.ResponseData.builder()
                    .detectorTemplates(new ArrayList<>()).totalCount(totalCount).build();
            return DetectorTemplateSearchResultObject.buildSuccessResponse(responseData);
        }

        List<DetectorTemplateCoreDataVO> templateCoreDataVOS = new ArrayList<>();
        for (DetectorTemplate template : templates) {
            DetectorTemplateCoreDataVO templateCoreDataVO = DetectorTemplateBuilder.buildCoreSimpleVOFromEntity(
                    template);
            templateCoreDataVO.setGeneralVO(DetectorTemplateBuilder.buildGeneralDataFromEntity(template));
            updateOwnerData(templateCoreDataVO.getOwnerVO());
            templateCoreDataVOS.add(templateCoreDataVO);
        }

        DetectorTemplateSearchResultObject.ResponseData responseData = DetectorTemplateSearchResultObject.ResponseData.builder()
                .detectorTemplates(templateCoreDataVOS).totalCount(totalCount).build();
        return DetectorTemplateSearchResultObject.buildSuccessResponse(responseData);
    }

    @Override
    @Transactional
    public void runTemplateAsync(Integer userId, Long templateId, Long taskId) {
        DetectorTemplate template;
        DetectorResult detectorResult;
        try {
            Optional<DetectorTemplate> templateOpt = detectorTemplateRepository.findById(templateId);
            if (templateOpt.isEmpty()) {
                throw new PMTemplateProcessingException("Can not find template, templateId=" + templateId);
            }

            template = templateOpt.get();

            detectorResult = processTemplate(template, userId);
        } catch (PMTemplateProcessingException e) {
            log.error("Can not process template, templateId={}", templateId, e);

            taskProgressRetriever.updateTaskProgress(taskId, TaskStatus.ERROR, null);

            return;
        } catch (Exception e) {
            log.error("Unexpected exception occur while process template, templateId={}", templateId, e);

            taskProgressRetriever.updateTaskProgress(taskId, TaskStatus.ERROR, null);
            return;
        }

        // Completed job progress
        taskProgressRetriever.updateTaskProgress(taskId, TaskStatus.COMPLETED,
                createReportResultActionVO(detectorResult));

        // Send notification and email
        notifyRunnerAboutResultCreated(Long.valueOf(userId), detectorResult, template.isMailReceived());
    }

    private ActionVO createReportResultActionVO(DetectorResult reportResult) {
        ReportResultActionDataVO reportResultActionDataVO = ReportResultActionDataVO.builder()
                .agencyId(reportResult.getAgencyId())
                .reportResultId(reportResult.getId())
                .build();

        return ActionVO.builder()
                .data(reportResultActionDataVO)
                .target(ActionTarget.DETECTOR_RESULT_DETAIL)
                .type(ActionType.REDIRECT)
                .btnTitle(TextKey.VIEW_BTN_TITLE)
                .build();
    }

    @Override
    public void scanTemplatesAsync() {
        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active templates
        List<DetectorTemplate> templates = detectorTemplateRepository.findAllByStatusAndDeleted(ACTIVE, false);
        if (ListUtil.hasNoItem(templates)) {
            log.debug("Have no any active template. So have no template triggered");
            return;
        }

        // Find all the templates that can be process
        List<DetectorTemplate> candidateTemplates = templates.stream()
                .filter(template -> TemplateScheduleHelper.isCandidateInTime(template.getSchedule(),
                        template.getTimezoneId(), now, offset)).toList();

        if (!candidateTemplates.isEmpty()) {
            List<Long> templateIds = candidateTemplates.stream().map(DetectorTemplate::getId).toList();
            log.debug("Found {} templates can be processed. Templates: {}", candidateTemplates.size(), templateIds);
            for (DetectorTemplate template : candidateTemplates) {
                try {
                    // Process templates. Should mark owner id as null (own by System)
                    DetectorResult detectorResult = processTemplate(template, null);
                    notifyRunnerAboutResultCreated(template.getOwnerId(), detectorResult, template.isMailReceived());
                } catch (DetectorTemplateProcessingException e) {
                    log.error("Process detector template, templateId={} failed!", template.getId(), e);
                } catch (Exception e) {
                    log.error("Unexpected errors while process template, templateId={}", template.getId(), e);
                }
            }
        }
    }

    private DetectorResult processTemplate(DetectorTemplate template, Integer userId)
            throws DetectorTemplateProcessingException {
        IntersectionInternalListVO intersectionListVO = findAllIntersectionsInTemplate(template);
        if (intersectionListVO == null || ListUtil.hasNoItem(intersectionListVO.getIntersections())) {
            String error = String.format("Template doesn't have any intersections, templateId=%d", template.getId());
            throw new DetectorTemplateProcessingException(error);
        }
        List<IntersectionInternalVO> intersections = intersectionListVO.getIntersections();

        Set<DetectorIntersectionResult> results = new HashSet<>();
        for (IntersectionInternalVO intersection : intersections) {
            log.debug("Start process report for intersection {}", intersection.getName());
            DetectorIntersectionResult result = detectorCalculator.process(template, intersection);
            results.add(result);
        }
        Pair<LocalDate, LocalDate> dateDate = template.getDateRange()
                .resolvePeriod(ZoneId.of(template.getTimezoneId()), AnalysisType.DETECTOR_REPORT);
        DetectorResult detectorResult = DetectorResult.builder()
                .agencyId(template.getAgencyId())
                .description(template.getDescription())
                .timezone(template.getTimezoneId())
                .fromDate(dateDate.getFirst())
                .toDate(dateDate.getSecond())
                .fromTime(template.getStartTime())
                .toTime(template.getEndTime())
                .weekDays(template.getWeekDays())
                .templateAggregation(template.getAggregation())
                .ownerId(Objects.nonNull(userId) ? Long.valueOf(userId) : null)
                .intersectionResults(results)
                .template(template)
                .name(template.getName())
                .createdAt(Timestamp.from(Instant.now()))
                .deleted(false)
                .build();

        if (!results.isEmpty()) {
            detectorResultRepository.save(detectorResult);

            for (DetectorIntersectionResult result : results) {
                result.setDetectorResult(detectorResult);
            }
            detectorIntersectionResultRepository.saveAll(results);
        }
        return detectorResult;
    }

    private IntersectionInternalListVO findAllIntersectionsInTemplate(DetectorTemplate template) {
        String intersectionListStr = template.getIntersectionList();
        log.debug("(findAllIntersectionsInTemplate)intersectionStr : {}", intersectionListStr);
        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId()).build();

        if (!IntersectionConstants.ALL_INTERSECTION_INDICATOR.equals(intersectionListStr)) {
            String[] currentIntIdsArr = template.getIntersectionList()
                    .split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR);
            log.debug("(findAllIntersectionsInTemplate)intersectionStr : {}", currentIntIdsArr);
            searchRequest.setIntersectionIds(Arrays.asList(currentIntIdsArr));
        }

        return intersectionStrategy.getIntersectionsByFilterInternal(searchRequest);
    }

    private Pair<List<DetectorTemplate>, Long> getTemplateAndCountFromDB(DetectorTemplateSearchRequestVO requestVO) {
        OrderSpecifier<String>[] orderSpecifiers = DetectorTemplateSearchHelper.createOrderBy(
                requestVO.getOrderByColumns());
        DetectorTemplateFilterData filterData = DetectorTemplateFilterData.builder().agencyId(requestVO.getAgencyId())
                .createdAtFrom(requestVO.getCreatedAtFrom()).createdAtTo(requestVO.getCreatedAtTo())
                .ownerId(requestVO.getOwnerId()).weekDays(requestVO.getWeekDays())
                .aggregation(requestVO.getAggregation()).orderByColumns(requestVO.getOrderByColumns())
                .status(requestVO.getStatus()).text(requestVO.getText()).build();
        List<DetectorTemplate> templates = detectorTemplateRepository.findPageByFilter(filterData, orderSpecifiers,
                requestVO.getPage(), requestVO.getSize());
        Long countTotal = detectorTemplateRepository.countToTalByFilter(filterData);
        return Pair.of(templates, countTotal);
    }

    private List<String> getAllIntersectionIds(DetectorTemplate template) {
        final String intersectionIdString = Objects.requireNonNull(template.getIntersectionList(),
                "Intersection list is null");
        if (!intersectionIdString.equals(IntersectionConstants.ALL_INTERSECTION_INDICATOR)) {
            return Arrays.asList(intersectionIdString.split(IntersectionConstants.INTERSECTION_SEPARATE_INDICATOR));
        }

        IntersectionSearchRequestVO searchRequest = IntersectionSearchRequestVO.builder()
                .agencyId(template.getAgencyId()).build();

        return intersectionStrategy.getIntersectionsByFilterInternal(searchRequest).getIntersections().stream()
                .map(IntersectionInternalVO::getId).toList();
    }

    private void updateOwnerData(SimpleOwnerVO ownerVO) {
        if (ownerVO == null) {
            throw new IllegalArgumentException();
        }

        try {
            studioUserService.findById(Math.toIntExact(ownerVO.getId())).ifPresent(userDto -> {
                ownerVO.setEmail(userDto.getEmail());
                ownerVO.setName(NameUtil.usFullName(userDto.getFirstName(), userDto.getLastName()));
            });
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
        }
    }

    /**
     * Update schedule data of a specific report template to MANUAL if:
     * <p>
     * 1. Current date range scope is RELATIVE
     * <p>
     * 2. New date range scope is SPECIFIC
     * <p>
     * 3. Current schedule scope is not MANUAL
     *
     * @param template  current report template
     * @param dateRange new date range
     */
    private void updateScheduleDataToManualIfNeed(DetectorTemplate template, DateRange dateRange) {
        DateRange currentDateRange;
        try {
            currentDateRange = BeanFinder.getDefaultObjectMapper()
                    .readValue(template.getDateRangeJson(), DateRange.class);
        } catch (JsonProcessingException e) {
            log.error("Invalid template: Can't read date range", e);
            throw new IllegalArgumentException("Invalid template: Can't read date range");
        }

        TemplateSchedule templateSchedule = template.getSchedule();
        if (currentDateRange.getScope() == DateRange.Scope.RELATIVE && dateRange.getScope() == DateRange.Scope.SPECIFIC && templateSchedule.getScope() != ScheduleScope.MANUAL) {
            templateSchedule.setScope(ScheduleScope.MANUAL);
            templateSchedule.setTime(null);
            templateSchedule.setValue(null);

            templateScheduleRepository.save(templateSchedule);
            log.debug("Successful update template schedule to MANUAL, templateId = {}", template.getId());
        }
    }

    private void notifyRunnerAboutResultCreated(Long userId, DetectorResult detectorResult, boolean mailReceive) {
        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(userId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found runner's information of result!");
            return;
        }

        List<StudioUserDto> userInternalVOList = Collections.singletonList(userOptional.get());
        reportNotificationStrategy.sendDetectorResultNotiAsync(detectorResult, userInternalVOList, mailReceive);
    }

    private void notifyOwnerAboutTemplateChange(DetectorTemplateDetailVO templateDetailVO,
                                                Long ownerId,
                                                TemplateConstant.ActionOnTemplate action) {
        // TODO: Consider disable this feature when owner modify template

        Optional<StudioUserDto> userOptional;
        try {
            userOptional = studioUserService.findById(Math.toIntExact(ownerId));
        } catch (StudioException e) {
            log.error(STUDIO_USER_ERROR_MSG, e);
            return;
        }

        if (userOptional.isEmpty()) {
            log.warn("Not found runner's information of result!");
            return;
        }

        List<StudioUserDto> userInternalVOList = Collections.singletonList(userOptional.get());
        reportNotificationStrategy.sendDetectorTemplateNotiAsync(templateDetailVO, userInternalVOList, action);
    }

    @Override
    @Transactional
    @AgencyAware(agencyId = "[0]")
    public void scanAgencyTemplatesAsync(int agencyId) {
        log.info("Scan scheduled detector templates for agency: {}", agencyId);

        Instant now = Instant.now();
        long offset = reportTemplateConfig.getScanningTemplateTimeOffset();
        // 1. Find all active templates
        List<DetectorTemplate> templates = detectorTemplateRepository.findAllByStatusAndDeleted(ACTIVE, false);
        if (ListUtil.hasNoItem(templates)) {
            log.debug("Have no any active template. So have no template triggered");
            return;
        }

        // Find all the templates that can be process
        List<DetectorTemplate> candidateTemplates = templates.stream()
                .filter(template -> TemplateScheduleHelper.isCandidateInTime(template.getSchedule(),
                        template.getTimezoneId(), now, offset)).toList();

        log.info("Found {} detector templates for agency: {}", candidateTemplates.size(), agencyId);

        if (!candidateTemplates.isEmpty()) {
            List<Long> templateIds = candidateTemplates.stream().map(DetectorTemplate::getId).toList();
            log.debug("Found {} templates can be processed. Templates: {}", candidateTemplates.size(), templateIds);
            for (DetectorTemplate template : candidateTemplates) {
                try {
                    // Process templates. Should mark owner id as null (own by System)
                    DetectorResult detectorResult = processTemplate(template, null);
                    notifyRunnerAboutResultCreated(template.getOwnerId(), detectorResult, template.isMailReceived());
                } catch (DetectorTemplateProcessingException e) {
                    log.error("Process detector template, templateId={} failed!", template.getId(), e);
                } catch (Exception e) {
                    log.error("Unexpected errors while process template, templateId={}", template.getId(), e);
                }
            }
        }
    }

}
