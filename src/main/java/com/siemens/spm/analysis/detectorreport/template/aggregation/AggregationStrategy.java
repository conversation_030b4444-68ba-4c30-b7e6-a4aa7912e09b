package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Strategy interface for different aggregation methods of detector reports.
 * Implementations of this interface provide specific aggregation logic (hourly, daily, weekly).
 */
public interface AggregationStrategy {

    /**
     * Aggregates detector reports based on the specific strategy implementation.
     *
     * @param template        The detector template containing configuration parameters
     * @param intersection    The intersection for which to generate reports
     * @param targetDateTimes The target date time for the report
     * @return List of aggregated detector reports
     */
    List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes);

    /**
     * Combines multiple period reports into a single aggregated report.
     *
     * @param totalReports List of individual period reports
     * @return Aggregated detector report
     */
    DetectorReportChartVO combinePeriodReports(List<DetectorReportChartVO> totalReports);

}