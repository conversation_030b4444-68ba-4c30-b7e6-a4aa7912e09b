---
description: Update pom.xml to add Maven dependencies & plugins
globs: pom.xml
alwaysApply: false
---
# Update pom.xml to add Maven dependencies & plugins

## System prompt characterization

Role definition: You are a Senior software engineer with extensive experience in Java software development

## Description

This rule provides a comprehensive, conversational approach to updating Maven pom.xml files with properties, dependencies, plugins, and profiles. It MUST ask questions first to understand the project needs and then conditionally add only relevant components.

**⚠️ CRITICAL: NO CACHING OF QUESTIONS ⚠️**
- NEVER use cached or remembered questions from previous interactions
- ALWAYS read the template file fresh using file_search and read_file tools
- Verify all questions and options match the current template content exactly

## Pre-requisites Check

**CRITICAL**: This step MUST be completed before proceeding to any subsequent steps. Do NOT continue with Step 1 (Project Assessment Questions) until the Maven Wrapper question is fully resolved.

### Maven Wrapper Check and Installation (MANDATORY FIRST STEP)

**First, check for Maven Wrapper files** in the project root:
- Look for `mvnw` (Unix/Mac) and `mvnw.cmd` (Windows) files
- Check for `.mvn/wrapper/` directory with `maven-wrapper.properties`

**If Maven Wrapper is NOT present:**

**STOP HERE** and ask the user: "I notice this project doesn't have Maven Wrapper configured. The Maven Wrapper ensures everyone uses the same Maven version, improving build consistency across different environments. Would you like me to install it? (y/n)"

**WAIT for the user's response. Do NOT proceed to any other questions or steps until this is resolved.**

**If user says yes:**
1. **Check if Maven is available**: First verify if `mvn` command is available by running `mvn --version`
2. **If Maven is not installed**: Provide installation instructions for their OS:
   - **macOS**: `brew install maven` (if Homebrew is available) or download from Apache Maven
   - **Windows**: Download from Apache Maven or use package managers like Chocolatey
   - **Linux**: Use package manager (`apt install maven`, `yum install maven`, etc.)

3. **Install Maven Wrapper**: Once Maven is available, run the wrapper installation command in the project root:
   ```bash
   mvn wrapper:wrapper
   ```

4. **Verify installation**: Check that the following files were created:
   - `mvnw` (executable script for Unix/Mac)
   - `mvnw.cmd` (batch script for Windows) 
   - `.mvn/wrapper/maven-wrapper.properties`
   - `.mvn/wrapper/maven-wrapper.jar`

5. **Update .gitignore** (if not already present): Ensure `.mvn` is tracked in Git, but add any IDE-specific files to .gitignore

**If user says no**: Note that system Maven will be used for validation steps.

**Benefits of Maven Wrapper:**
- Ensures consistent Maven version across all environments
- No need for developers to manually install Maven
- Reduces "works on my machine" issues
- Easier CI/CD pipeline setup

**ONLY AFTER** the Maven Wrapper question is completely resolved (installation completed or user declined), proceed to Step 1.

## Step 1: MANDATORY Conversational Assessment

**CRITICAL**: You MUST ask the exact questions from the template in strict order before making any changes to understand the project needs. Based on the answers, you will conditionally add only relevant dependencies and plugins.

### Project Assessment Questions (STRICT TEMPLATE COMPLIANCE)

**MANDATORY PROCESS:**

1. **ALWAYS READ THE TEMPLATE FRESH**: Before asking any questions, you MUST use the `file_search` and `read_file` tools to locate and read the complete content of `java-maven-questions-template.md`. DO NOT use cached or remembered questions from previous interactions.

2. **Reference the Template**: Use the questions from [java-maven-questions-template.md](mdc:.cursor/rules/templates/java-maven-questions-template.md)

3. **STRICT ENFORCEMENT RULES:**
   - ✅ Ask questions **ONE BY ONE** in the exact order specified in the template
   - ✅ **WAIT** for user response to each question before proceeding to the next
   - ✅ Use the **EXACT wording** from the template questions
   - ✅ Present the **EXACT options** listed in the template
   - ✅ Include **recommendations** when specified in the template
   - ✅ **Do NOT** ask all questions simultaneously
   - ✅ **Do NOT** assume answers or provide defaults
   - ✅ **Do NOT** skip questions or change their order

4. **QUESTION SEQUENCE** (from template - MUST BE READ FRESH EACH TIME):
   - **Question 1**: [Project Nature](mdc:.cursor/rules/templates/java-maven-questions-template.md#1-project-nature) (single selection from the template)
   - **Question 2**: [Java Version](mdc:.cursor/rules/templates/java-maven-questions-template.md#2-java-version) (single selection from the template)
   - **Question 3**: [Build and Quality Aspects](mdc:.cursor/rules/templates/java-maven-questions-template.md#3-build-and-quality-aspects) (multiple selection allowed from the template)
   - **Question 4**: [Coverage Threshold](mdc:.cursor/rules/templates/java-maven-questions-template.md#4-coverage-threshold) (conditional - only if coverage selected)
   - **Question 5**: [Sonar Configuration](mdc:.cursor/rules/templates/java-maven-questions-template.md#5-sonar-configuration-conditional) (conditional - only if static analysis with Sonar selected)
   - **Question 6**: [Sonar Host Configuration](mdc:.cursor/rules/templates/java-maven-questions-template.md#6-sonar-host-configuration-conditional) (conditional - only if Sonar configuration enabled)

5. **TEMPLATE VERIFICATION STEP:**
   - Before asking questions, verify that ALL options from the template are included
   - Cross-check your question content against the freshly read template file
   - If there are discrepancies, re-read the template and correct your questions

6. **VALIDATION CHECKPOINT:**
   - **STOP** and verify all applicable questions have been answered
   - **Do NOT proceed** to Step 2 until complete responses received
   - **Confirm** understanding of user selections before implementation

**TEMPLATE ADHERENCE IS MANDATORY** - Follow the template questions exactly as written, maintaining their structure, options, and recommendations.

## Step 2: Conditional Configuration Based on Answers

**⚠️ CRITICAL TEMPLATE COMPLIANCE REQUIREMENT ⚠️**

**ALL plugin configurations MUST exactly match the templates in `java-maven-plugins-template.md`. Do NOT deviate from template specifications. This includes:**

- ✅ **Property Naming Convention**: Use `maven-plugins-*` format (e.g., `maven-plugins-compiler.version`, NOT `maven-compiler-plugin.version`)
- ✅ **Complete Compiler Configuration**: Include ALL Error Prone and NullAway options as specified in template:
  - `-Xlint:all` and `-Werror` compiler arguments
  - `--should-stop=ifError=FLOW`
  - Full NullAway configuration with `JSpecifyMode=true`, `TreatGeneratedAsUnannotated=true`, etc.
- ✅ **Full Enforcer Plugin Setup**: Include `extra-enforcer-rules` dependency and all specified rules
- ✅ **Exact Profile Structure**: Follow template's `<build>` vs `<reporting>` section separation
- ✅ **Required Supporting Files**: Create `.mvn/jvm.config` when Error Prone is enabled
- ✅ **Package Name Customization**: Update `AnnotatedPackages` and `targetClasses` to match actual project structure

**TEMPLATE ADHERENCE IS MANDATORY - No exceptions or "simplified" versions allowed.**

After getting answers, implement the configuration following this order:

### 2.1 Properties Section (Interactive Configuration)

**Reference**: For detailed properties configuration strategy and conditional logic, see [java-maven-properties-template.md](mdc:.cursor/rules/templates/java-maven-properties-template.md)

**Summary**: Build properties incrementally based on user's actual needs and project requirements. The template provides comprehensive guidance on:
- Core properties (always added)
- Maven version and plugin properties (conditional)
- Dependency version properties (based on selections)
- Quality and analysis properties (with custom thresholds)
- Feature-specific plugin version properties
- Implementation guidelines

### 2.2 Dependencies Section (Optional)

**Dependency Strategy**: Add only essential dependencies that enhance code quality and safety.

**Reference**: For specific dependency configurations, see [java-maven-deps-template.md](mdc:.cursor/rules/templates/java-maven-deps-template.md)

#### Available Dependencies (add only if selected):
- **JSpecify**: Nullness annotations for improved null safety

#### Implementation Approach:
1. **Ask about code quality dependencies**: Follow the questions in the template file
2. **Add only selected dependencies**: Don't include dependencies the user doesn't need  
3. **Configure with appropriate scope**: Use `provided` scope for compile-time only dependencies

### 2.3 Build Plugins Section (Conditional)

**Plugin Configuration Strategy**: Add plugins based only on selected features and user needs.

**Reference**: For specific plugin configurations, see [java-maven-plugins-template.md](mdc:.cursor/rules/templates/java-maven-plugins-template.md)

#### Plugin Selection Process:

**Core Plugins** (ask if user wants enhanced enforcement):
- **maven-compiler-plugin**: Enhanced with Error Prone and NullAway analysis
- **maven-enforcer-plugin**: With dependency convergence and circular dependency checks  
- **maven-surefire-plugin**: For unit testing with proper configuration

**Feature-Based Plugins** (add only if selected):
- **Integration Testing**: maven-failsafe-plugin
- **Code Coverage**: jacoco-maven-plugin (in profile)
- **Mutation Testing**: pitest-maven (in profile)
- **Security Scanning**: dependency-check-maven (in profile)
- **Static Analysis**: spotbugs-maven-plugin, maven-pmd-plugin (in profile) & sonar (in profile)
- **Version Management**: versions-maven-plugin
- **Build Info**: git-commit-id-plugin
- **Library Publishing**: flatten-maven-plugin

#### Implementation Approach:
1. **Ask about enhancement level**: "Do you want enhanced code analysis (Error Prone, NullAway)? (y/n)"
2. **Add core plugins** based on enhancement choice
3. **Add feature plugins** only for selected features
4. **Configure profiles** for optional analysis tools
5. **Customize package names** in plugin configurations

### 2.4 Profiles Section (Conditional)

**Profile Strategy**: Create profiles only for selected optional features to avoid overwhelming the default build.

**Reference**: For specific profile configurations, see [java-maven-plugins-template.md](mdc:.cursor/rules/templates/java-maven-plugins-template.md)

**Available Profiles** (add only if features selected):
- `jacoco`: Code coverage analysis
- `pitest`: Mutation testing  
- `security`: OWASP dependency security scanning
- `find-bugs`: Static analysis with SpotBugs and PMD

#### Profile Usage Examples:
Only provide examples for profiles that were actually added based on user selections.

## Step 3: Final Validation

After all updates are complete, validate the configuration:

### If using Maven Wrapper (recommended):
```bash
./mvnw validate
```

### If using system Maven:
```bash
mvn validate
```

**Note**: Always prefer using the Maven Wrapper (./mvnw) if it was installed during the pre-requisites check, as it ensures consistent build behavior across different environments.

## Step 4: Provide Usage Examples

Only provide examples for the features that were actually added based on user selections.

## Implementation Guidelines

1. **ALWAYS ask the questions first** - Do not make assumptions about what the user needs
2. **STRICTLY follow template specifications** - Copy configurations exactly from `java-maven-plugins-template.md` without modifications
3. **Add only what was requested** - Don't include everything by default
4. **Follow the order**: Questions → Properties → Dependencies → Plugins → Profiles
5. **Customize package names**: Update package structure to match the actual project
6. **Adjust thresholds**: Use the coverage levels specified by the user
7. **Configure Sonar parameters**: Replace template placeholders with user-provided values:
   - Replace `YOUR_GITHUB_USER` with the provided organization identifier
   - Replace `YOUR_GITHUB_USER_REPOSITORY_NAME` with the provided project key
   - Replace `YOUR_PROJECT_NAME` with the provided project display name
   - Set `sonar.host.url` based on the selected Sonar service (SonarCloud or custom SonarQube)
8. **Validate configuration**: Always run validation after changes
9. **Provide relevant examples**: Only show usage examples for added features

## Conversation Flow Example

1. Start with: "I'll help you improve your pom.xml with Maven best practices. Let me ask a few questions to understand your project needs."
2. Ask all relevant questions from Step 1
3. Based on answers, explain what will be added: "Based on your selections, I'll add..."
4. Implement only the requested features
5. Validate the configuration
6. Provide usage examples for the added features

This approach ensures the pom.xml is tailored to the actual project needs rather than adding everything by default.
